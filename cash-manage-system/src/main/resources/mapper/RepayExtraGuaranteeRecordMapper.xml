<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jinghang.cash.mapper.RepayExtraGuaranteeRecordMapper">

    <resultMap id="BaseResultMap" type="com.jinghang.cash.pojo.RepayExtraGuaranteeRecord">
        <id property="id" column="id" jdbcType="VARCHAR"/>
        <result property="repayPlanId" column="repay_plan_id" jdbcType="VARCHAR"/>
        <result property="sourceRepayId" column="source_repay_id" jdbcType="VARCHAR"/>
        <result property="loanId" column="loan_id" jdbcType="VARCHAR"/>
        <result property="period" column="period" jdbcType="INTEGER"/>
        <result property="repayApplyTime" column="repay_apply_time" jdbcType="TIMESTAMP"/>
        <result property="repayPurpose" column="repay_purpose" jdbcType="VARCHAR"/>
        <result property="repayMode" column="repay_mode" jdbcType="VARCHAR"/>
        <result property="guaranteeAmt" column="guarantee_amt" jdbcType="DECIMAL"/>
        <result property="agreementNo" column="agreement_no" jdbcType="VARCHAR"/>
        <result property="repayState" column="repay_state" jdbcType="VARCHAR"/>
        <result property="failReason" column="fail_reason" jdbcType="VARCHAR"/>
        <result property="repaidTime" column="repaid_time" jdbcType="TIMESTAMP"/>
        <result property="feeType" column="fee_type" jdbcType="VARCHAR"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
        <result property="revision" column="revision" jdbcType="INTEGER"/>
        <result property="createdBy" column="created_by" jdbcType="VARCHAR"/>
        <result property="updatedBy" column="updated_by" jdbcType="VARCHAR"/>
        <result property="createdTime" column="created_time" jdbcType="TIMESTAMP"/>
        <result property="updatedTime" column="updated_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,repay_plan_id,source_repay_id,loan_id,period,repay_apply_time,repay_purpose,
        repay_mode,guarantee_amt,agreement_no,repay_state,fail_reason,repaid_time,fee_type,remark,revision,created_by,updated_by,created_time,updated_time
    </sql>
    <select id="findTopByLoanIdAndPeriodAndRepayState"
            resultType="com.jinghang.cash.pojo.RepayExtraGuaranteeRecord">
        select<include refid="Base_Column_List"/> from repay_extra_guarantee_record
        where loan_id = #{loanId} and period = #{period} and repay_state = #{repayState}
        order by repay_apply_time desc
        limit 1
    </select>

</mapper>
