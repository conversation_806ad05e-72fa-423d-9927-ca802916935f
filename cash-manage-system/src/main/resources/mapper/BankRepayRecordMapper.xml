<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jinghang.cash.mapper.BankRepayRecordMapper">

    <resultMap id="BaseResultMap" type="com.jinghang.cash.pojo.BankRepayRecord">
            <id property="id" column="id" jdbcType="VARCHAR"/>
            <result property="sourceRecordId" column="source_record_id" jdbcType="VARCHAR"/>
            <result property="loanId" column="loan_id" jdbcType="VARCHAR"/>
            <result property="period" column="period" jdbcType="INTEGER"/>
            <result property="repayPurpose" column="repay_purpose" jdbcType="VARCHAR"/>
            <result property="repayMode" column="repay_mode" jdbcType="VARCHAR"/>
            <result property="repayType" column="repay_type" jdbcType="VARCHAR"/>
            <result property="amount" column="amount" jdbcType="DECIMAL"/>
            <result property="principal" column="principal" jdbcType="DECIMAL"/>
            <result property="interest" column="interest" jdbcType="DECIMAL"/>
            <result property="guarantee" column="guarantee" jdbcType="DECIMAL"/>
            <result property="penalty" column="penalty" jdbcType="DECIMAL"/>
            <result property="breach" column="breach" jdbcType="DECIMAL"/>
            <result property="state" column="state" jdbcType="VARCHAR"/>
            <result property="failReason" column="fail_reason" jdbcType="VARCHAR"/>
            <result property="bankRepayNo" column="bank_repay_no" jdbcType="VARCHAR"/>
            <result property="remark" column="remark" jdbcType="VARCHAR"/>
            <result property="revision" column="revision" jdbcType="VARCHAR"/>
            <result property="createdBy" column="created_by" jdbcType="VARCHAR"/>
            <result property="createdTime" column="created_time" jdbcType="TIMESTAMP"/>
            <result property="updatedBy" column="updated_by" jdbcType="VARCHAR"/>
            <result property="updatedTime" column="updated_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,source_record_id,loan_id,
        period,repay_purpose,repay_mode,
        repay_type,amount,principal,
        interest,guarantee,penalty,
        breach,state,fail_reason,
        bank_repay_no,remark,revision,
        created_by,created_time,updated_by,
        updated_time
    </sql>
</mapper>
