<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.jinghang.cash.modules.project.mapper.GuaranteeConfigMapper">
    <resultMap id="BaseResultMap" type="com.jinghang.cash.modules.project.domain.GuaranteeConfig">
        <id column="id" property="id"/>
        <result column="guarantee_code" property="guaranteeCode"/>
        <result column="guarantee_name" property="guaranteeName"/>
        <result column="guarantee_name_short" property="guaranteeNameShort"/>
        <result column="guarantee_desc" property="guaranteeDesc"/>
        <result column="contact_person" property="contactPerson"/>
        <result column="contact_phone" property="contactPhone"/>
        <result column="email_address" property="emailAddress"/>
        <result column="enabled" property="enabled"/>
        <result column="remark" property="remark"/>
        <result column="revision" property="revision"/>
        <result column="created_by" property="createdBy"/>
        <result column="created_time" property="createdTime"/>
        <result column="updated_by" property="updatedBy"/>
        <result column="updated_time" property="updatedTime"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, guarantee_code, guarantee_name, guarantee_name_short, guarantee_desc, contact_person, contact_phone, email_address, enabled, remark, revision, created_by, created_time, updated_by, updated_time
    </sql>

    <select id="findAll" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from guarantee_config
        <where>
        </where>
        order by id desc
    </select>
</mapper>