package com.jinghang.cash.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jinghang.cash.pojo.Order;
import com.jinghang.cash.service.OrderService;
import com.jinghang.cash.mapper.OrderMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【order(订单)】的数据库操作Service实现
* @createDate 2023-11-15 15:48:19
*/
@Service
public class OrderServiceImpl extends ServiceImpl<OrderMapper, Order>
    implements OrderService{

}




