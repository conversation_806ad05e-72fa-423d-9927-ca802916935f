package com.jinghang.cash.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jinghang.cash.pojo.RightsOrder;
import com.jinghang.cash.service.RightsOrderService;
import com.jinghang.cash.mapper.RightsOrderMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【rights_order(权益订单)】的数据库操作Service实现
* @createDate 2023-11-16 11:45:25
*/
@Service
public class RightsOrderServiceImpl extends ServiceImpl<RightsOrderMapper, RightsOrder>
    implements RightsOrderService{


    @Override
    public RightsOrder getSuccessOrderById(String orderId, String state) {
        return baseMapper.selectOne(new LambdaQueryWrapper<RightsOrder>().eq(RightsOrder::getOrderId, orderId).eq(RightsOrder::getState, state));
    }
}




