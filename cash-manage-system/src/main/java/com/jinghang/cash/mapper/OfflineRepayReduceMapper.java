package com.jinghang.cash.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.github.pagehelper.Page;
import com.jinghang.cash.modules.manage.vo.req.OfflineRepayReduceReq;
import com.jinghang.cash.modules.manage.vo.res.OfflineRepayReduceResponse;
import com.jinghang.cash.pojo.OfflineRepayReduce;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
@DS("slave")
public interface OfflineRepayReduceMapper extends BaseMapper<OfflineRepayReduce> {

    List<OfflineRepayReduce> search(OfflineRepayReduce entity);
    Page<OfflineRepayReduceResponse> searchPage(OfflineRepayReduceReq param);

    void updateBathUseStateByIds(List<String> ids);
}
