package com.jinghang.cash.pojo;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * @TableName user_face
 */
@TableName(value ="user_face")
@Data
public class UserFace implements Serializable {
    private String id;

    private String userId;

    private String faceChannel;

    private Date faceTime;

    private Integer faceScore;

    private String ossBucket;

    private String ossKey;

    private String remark;

    private String revision;

    private String createdBy;

    private Date createdTime;

    private String updatedBy;

    private Date updatedTime;

    private static final long serialVersionUID = 1L;
}