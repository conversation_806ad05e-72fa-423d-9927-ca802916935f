package com.jinghang.cash.pojo;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * @TableName user_file
 */
@TableName(value ="user_file")
@Data
public class UserFile implements Serializable {
    private String id;

    private String userId;

    private String loanStage;

    private String fileType;

    private String fileName;

    private String ossBucket;

    private String ossKey;

    private String signFinal;

    private String remark;

    private String revision;

    private String createdBy;

    private Date createdTime;

    private String updatedBy;

    private Date updatedTime;

    private static final long serialVersionUID = 1L;
}
