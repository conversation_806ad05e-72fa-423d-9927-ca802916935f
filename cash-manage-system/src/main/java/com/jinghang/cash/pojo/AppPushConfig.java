package com.jinghang.cash.pojo;

import com.baomidou.mybatisplus.annotation.TableName;
import com.jinghang.cash.enums.ClientNum;
import com.jinghang.cash.enums.WhetherState;
import com.jinghang.cash.mapper.BaseEntity;
import lombok.Data;

import javax.persistence.EnumType;
import javax.persistence.Enumerated;

/**
 * @TableName app_push_config
 */
@TableName(value = "app_push_config")
@Data
public class AppPushConfig extends BaseEntity {
    @Enumerated(EnumType.STRING)
    private ClientNum clientNum;

    private String versionName;

    private String versionNum;

    @Enumerated(EnumType.STRING)
    private WhetherState pushType;

    private String pushEnable;

    private String pushContext;

    private String pushUrl;


}
