package com.jinghang.cash.pojo;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.Version;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.jinghang.cash.enums.AuditStatus;
import com.jinghang.cash.enums.RepayPurpose;
import com.jinghang.cash.enums.UseState;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 线下还款减免表
 *
 * <AUTHOR>
 */
@Data
@TableName(value = "offline_repay_reduce")
public class OfflineRepayReduce {
    /**
     * 主键id
     */
    @TableId
    private String id;
    /**
     * 订单号
     */
    @TableField(value = "order_id")
    private String orderId;
    /**
     * 借据号
     */
    @TableField(value = "loan_id")
    private String loanId;
    /**
     * 期数
     */
    @TableField(value = "period")
    private Integer period;
    /**
     * 还款模式
     */
    @TableField(value = "repay_purpose")
    private RepayPurpose repayPurpose;

    /**
     * 应还本金
     */
    @TableField(value = "principal_amt")
    private BigDecimal principalAmt;
    /**
     * 应还利息
     */
    @TableField(value = "interest_amt")
    private BigDecimal interestAmt;
    /**
     * 应还担保费
     */
    @TableField(value = "guarantee_amt")
    private BigDecimal guaranteeAmt;
    /**
     * 应还罚息
     */
    @TableField(value = "penalty_amt")
    private BigDecimal penaltyAmt;
    /**
     * 应还咨询费
     */
    @TableField(value = "consult_fee")
    private BigDecimal consultFee;
    /**
     * 应还总金额
     */
    @TableField(value = "amount")
    private BigDecimal amount;
    /**
     * 减免金额
     */
    @TableField(value = "reduce_amount")
    private BigDecimal reduceAmount;

    /**
     * 实还总金额
     */
    @TableField(value = "act_amount")
    private BigDecimal actAmount;

    /**
     * 审核状态
     */
    @TableField(value = "audit_state")
    private AuditStatus auditState;

    /**
     * 使用状态
     */
    @TableField(value = "use_state")
    private UseState useState;
    /**
     * 备注
     */
    @TableField(value = "remark", fill = FieldFill.INSERT)
    private String remark;
    /**
     * 版本号
     */
    @TableField(value = "revision", fill = FieldFill.INSERT)
    @Version
    private String revision;
    /**
     * 创建人
     */
    @TableField(value = "created_by", fill = FieldFill.INSERT)
    private String createdBy;
    /**
     * 创建时间
     */
    @TableField(value = "created_time", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createdTime;
    /**
     * 更新人
     */
    @TableField(value = "updated_by", fill = FieldFill.UPDATE)
    private String updatedBy;
    /**
     * 更新时间
     */
    @TableField(value = "updated_time", fill = FieldFill.UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updatedTime;

}
