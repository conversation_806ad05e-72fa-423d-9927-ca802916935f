package com.jinghang.cash.pojo;

import com.jinghang.cash.enums.FlowChannel;

import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import java.time.LocalDateTime;

public class OrderEventRecord {
    /**
     * 主键
     */
    private String id;
    /**
     * 订单id
     */
    private String orderId;

    /**
     * 流量渠道
     */
    @Enumerated(EnumType.STRING)
    private FlowChannel flowChannel;

    /**
     * 用户id
     */
    private String userId;

    /**
     * 订单事件
     */
    private String orderEvent;

    /**
     * 事件事件
     */
    private LocalDateTime eventTime;

    /**
     * 备注
     */
    private String remark;
    /**
     * 乐观锁
     */
    private Integer revision;
    /**
     * 创建人
     */
    private String createdBy;
    /**
     * 创建时间
     */
    private LocalDateTime createdTime;
    /**
     * 更新人
     */
    private String updatedBy;
    /**
     * 更新时间
     */
    private LocalDateTime updatedTime;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public FlowChannel getFlowChannel() {
        return flowChannel;
    }

    public void setFlowChannel(FlowChannel flowChannel) {
        this.flowChannel = flowChannel;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getOrderEvent() {
        return orderEvent;
    }

    public void setOrderEvent(String orderEvent) {
        this.orderEvent = orderEvent;
    }

    public LocalDateTime getEventTime() {
        return eventTime;
    }

    public void setEventTime(LocalDateTime eventTime) {
        this.eventTime = eventTime;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Integer getRevision() {
        return revision;
    }

    public void setRevision(Integer revision) {
        this.revision = revision;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public LocalDateTime getCreatedTime() {
        return createdTime;
    }

    public void setCreatedTime(LocalDateTime createdTime) {
        this.createdTime = createdTime;
    }

    public String getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }

    public LocalDateTime getUpdatedTime() {
        return updatedTime;
    }

    public void setUpdatedTime(LocalDateTime updatedTime) {
        this.updatedTime = updatedTime;
    }
}
