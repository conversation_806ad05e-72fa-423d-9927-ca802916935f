package com.jinghang.cash.pojo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 权益配置
 *
 * @TableName rights_config
 */
@TableName(value = "rights_config")
@Data
public class RightsConfig implements Serializable {
    /**
     * 主键
     */
    @TableId(value = "id")
    private String id;

    /**
     * 风险等级
     */
    @TableField(value = "risk_level")
    private Integer riskLevel;

    /**
     * 权益强制购买
     */
    @TableField(value = "approve_rights_force")
    private String approveRightsForce;

    /**
     * 售价
     */
    @TableField(value = "selling_price")
    private BigDecimal sellingPrice;
    /**
     * 权益基础包id
     */
    @TableField(value = "rights_package_id")
    private String rightsPackageId;

    /**
     * 是否可用
     */
    @TableField(value = "use_status")
    private String useStatus;
    /**
     * 备注
     */
    @TableField(value = "remark")
    private String remark;

    /**
     * 乐观锁
     */
    @TableField(value = "revision")
    private Integer revision;

    /**
     * 创建人
     */
    @TableField(value = "created_by")
    private String createdBy;

    /**
     * 创建时间
     */
    @TableField(value = "created_time")
    private LocalDateTime createdTime;

    /**
     * 更新人
     */
    @TableField(value = "updated_by")
    private String updatedBy;

    /**
     * 更新时间
     */
    @TableField(value = "updated_time")
    private LocalDateTime updatedTime;

    /**
     * 流量渠道
     */
    @TableField(value = "flow_channel")
    private String flowChannel;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
