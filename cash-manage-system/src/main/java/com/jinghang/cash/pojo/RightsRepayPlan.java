package com.jinghang.cash.pojo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 权益扣费计划
 *
 * <AUTHOR>
 * @TableName rights_repay_plan
 */
@TableName(value = "rights_repay_plan")
@Data
public class RightsRepayPlan {
    /**
     * 主键
     */
    private String id;

    /**
     * 备注
     */
    @TableField(value = "remark")
    private String remark;

    /**
     * 乐观锁
     */
    @TableField(value = "revision")
    private Integer revision;

    /**
     * 创建人
     */
    @TableField(value = "created_by")
    private String createdBy;

    /**
     * 创建时间
     */
    @TableField(value = "created_time")
    private LocalDateTime createdTime;

    /**
     * 更新人
     */
    @TableField(value = "updated_by")
    private String updatedBy;

    /**
     * 更新时间
     */
    @TableField(value = "updated_time")
    private LocalDateTime updatedTime;

    /**
     * 权益金额
     */
    @TableField(value = "rights_amount")
    private BigDecimal rightsAmount;

    /**
     * 应还时间
     */
    @TableField(value = "should_repay_time")
    private LocalDate shouldRepayTime;

    /**
     * 实还时间
     */
    @TableField(value = "actual_repay_time")
    private LocalDateTime actualRepayTime;

    /**
     * 权益还款状态;权益还款状态： 默认待还 。待还/已还
     */
    @TableField(value = "rights_repay_status")
    private String rightsRepayStatus;

    /**
     * 权益还款计划状态;权益扣费计划状态：正常/取消
     */
    @TableField(value = "status")
    private String status;

    /**
     * 借据id
     */
    @TableField(value = "loan_id")
    private String loanId;

    /**
     * 订单id
     */
    @TableField(value = "order_id")
    private String orderId;
    /**
     * 用户id
     */
    @TableField(value = "user_id")
    private String userId;

}
