package com.jinghang.cash.modules.project.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jinghang.cash.api.dto.ProjectElementsDto;
import com.jinghang.cash.api.enums.AbleStatusExt;
import com.jinghang.cash.api.enums.ProjectDurationType;
import com.jinghang.cash.modules.project.domain.ProjectElements;
import com.jinghang.cash.modules.project.domain.dto.ProjectElementsQueryCriteria;
import com.jinghang.cash.modules.project.mapper.ProjectElementsMapper;
import com.jinghang.cash.modules.project.service.ProjectElementsService;
import com.jinghang.cash.modules.project.service.ProjectInfoService;
import com.jinghang.cash.modules.project.service.domain.vo.ProjectElementsVo;
import com.jinghang.cash.utils.PageResult;
import com.jinghang.cash.utils.SecurityUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
* @description 服务实现
* <AUTHOR>
* @date 2025-08-25
**/
@Service
@RequiredArgsConstructor
public class ProjectElementsServiceImpl extends ServiceImpl<ProjectElementsMapper, ProjectElements> implements ProjectElementsService {

    private final ProjectElementsMapper projectElementsMapper;

    @Autowired
    private ProjectInfoService projectInfoService;

    @Override
    public PageResult<ProjectElements> queryAll(ProjectElementsQueryCriteria criteria, Page<Object> page){
        //return PageUtil.toPage(projectElementsMapper.findAll(criteria, page));
        return null;
    }

    @Override
    public List<ProjectElements> queryAll(ProjectElementsQueryCriteria criteria){
        LambdaQueryWrapper<ProjectElements> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        if(ObjectUtil.isNotEmpty(criteria.getProjectCode())){
            lambdaQueryWrapper.eq(ProjectElements::getProjectCode,criteria.getProjectCode());
        }
        return projectElementsMapper.selectList(lambdaQueryWrapper);
    }

    @Override
    public ProjectElementsVo queryProjectElementsInfo(String projectCode) {
        ProjectElementsVo vo = new ProjectElementsVo();
        LambdaQueryWrapper<ProjectElements> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        if(ObjectUtil.isNotEmpty(projectCode)){
            lambdaQueryWrapper.eq(ProjectElements::getProjectCode,projectCode);
        }
        ProjectElements elements = projectElementsMapper.selectOne(lambdaQueryWrapper);
        BeanUtil.copyProperties(elements,vo);
        return vo;
    }

    @Override
    public ProjectElementsVo queryProjectElementsInfoById(String id) {
        ProjectElementsVo vo = new ProjectElementsVo();
        ProjectElements elements = projectElementsMapper.selectById(id);
        BeanUtil.copyProperties(elements,vo);
        return vo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void create(ProjectElementsDto resources) {
        String userId = String.valueOf(SecurityUtils.getCurrentUserId());
        LocalDateTime dateTime = LocalDateTime.now();
        ProjectElements elements = new ProjectElements();
        BeanUtil.copyProperties(resources, elements);
        String elemId = "ELEM" + System.currentTimeMillis() + String.valueOf(Math.random()).substring(2,5) ;
        elements.setId(elemId);
        elements.setCreatedBy(userId);
        elements.setCreatedTime(dateTime);
        elements.setEnabled(AbleStatusExt.INIT);
        elements.setProjectDurationType(ProjectDurationType.TEMPORARY);
        projectElementsMapper.insert(elements);
        // 清除项目信息缓存
        projectInfoService.clearProjectInfoCache(resources.getProjectCode());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(ProjectElementsDto resources) {
        ProjectElements projectElements = getById(resources.getId());
        projectElements.copy(resources);
        projectElements.setUpdatedBy(String.valueOf(SecurityUtils.getCurrentUserId()));
        projectElements.setUpdatedTime(LocalDateTime.now());
        projectElements.copy(resources);
        projectElementsMapper.updateById(projectElements);
        // 清除项目信息缓存
        projectInfoService.clearProjectInfoCache(resources.getProjectCode());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteAll(List<String> ids) {
        // 先获取要删除的项目要素信息，用于清除缓存
        List<ProjectElements> elementsToDelete = projectElementsMapper.selectBatchIds(ids);

        projectElementsMapper.deleteBatchIds(ids);

        // 清除相关项目的缓存
        for (ProjectElements element : elementsToDelete) {
            projectInfoService.clearProjectInfoCache(element.getProjectCode());
        }
    }

    @Override
    public void download(List<ProjectElements> all, HttpServletResponse response) throws IOException {
        List<Map<String, Object>> list = new ArrayList<>();
        for (ProjectElements projectElements : all) {
            Map<String,Object> map = new LinkedHashMap<>();
            map.put("关联的项目唯一编码", projectElements.getProjectCode());
            map.put("可提现范围(元) (格式 如 1000-50000)", projectElements.getDrawableAmountRange());
            map.put("单笔提现步长(元)", projectElements.getDrawableAmountStep());
            map.put("授信黑暗期 (格式 HH:mm-HH:mm)", projectElements.getCreditDarkHours());
            map.put("用信黑暗期 (格式 HH:mm-HH:mm)", projectElements.getLoanDarkHours());
            map.put("还款黑暗期 (格式 HH:mm-HH:mm)", projectElements.getRepayDarkHours());
            map.put("资金方授信黑暗期", projectElements.getFundingCreditDarkHours());
            map.put("资金方用信黑暗期", projectElements.getFundingLoanDarkHours());
            map.put("资金方还款黑暗期", projectElements.getFundingRepayDarkHours());
            map.put("日授信限额(万元)", projectElements.getDailyCreditLimit());
            map.put("日放款限额(万元)", projectElements.getDailyLoanLimit());
            map.put("授信锁定期限(天)", projectElements.getCreditLockDays());
            map.put("用信锁定期限(天)", projectElements.getLoanLockDays());
            map.put("对客利率(%)", projectElements.getCustomerInterestRate());
            map.put("对资利率(%)", projectElements.getFundingInterestRate());
            map.put("年龄范围(岁) (格式 如 22-55 左右包含)", projectElements.getAgeRange());
            map.put("支持的还款类型 (英文逗号分隔)", projectElements.getSupportedRepayTypes());
            map.put("借款期限 (英文逗号分隔)", projectElements.getLoanTerms());
            map.put("资方路由", projectElements.getCapitalRoute());
            //map.put("备注", projectElements.getRemark());
            map.put("乐观锁", projectElements.getRevision());
            map.put("创建人", projectElements.getCreatedBy());
            map.put("创建时间", projectElements.getCreatedTime());
            map.put("更新人", projectElements.getUpdatedBy());
            map.put("更新时间", projectElements.getUpdatedTime());
            map.put("项目时效类型（LONGTIME, TEMPORARY）", projectElements.getProjectDurationType());
            map.put("临时配置有效期起", projectElements.getTempStartTime());
            map.put("临时配置有效期止", projectElements.getTempEndTime());
            map.put("启用状态", projectElements.getEnabled());
            map.put("是否年结", projectElements.getGraceNext());
            list.add(map);
        }
        //FileUtil.downloadExcel(list, response);
    }
}