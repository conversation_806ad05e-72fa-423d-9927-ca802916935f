package com.jinghang.cash.modules.manage.vo.req;

import com.jinghang.cash.enums.RightsStatus;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 取消权益订单
 *
 * <AUTHOR>
 * @date 2024/3/19
 */
@Data
public class CancelRightsRepayPlanReq {

    /**
     * 权益扣费计划
     */
    @NotBlank(message = "权益扣费计划id不能为空")
    private String rightRepayPlanId;
    /**
     * 权益扣费计划状态
     * @link com.jinghang.cash.enums.RightsStatus
     */
    @NotBlank(message = "权益扣费计划状态不能为空")
    private RightsStatus status;
}
