package com.jinghang.cash.modules.manage.vo.req;

import com.jinghang.cash.modules.manage.vo.PageParam;
import lombok.Data;

/**
 * 订单详情请求参数
 */
@Data
public class OrderListReq extends PageParam {

    /**
     * 手机号
     */
    private String mobile;
    /**
     * 订单号
     */
    private String orderId;
    /**
     * 身份证号
     */
    private String certNo;
    /**
     * 开始时间
     */
    private String startTime;
    /**
     * 结束时间
     */
    private String endTime;
    /**
     * 订单状态
     */
    private String orderState;
    /**
     * 放款渠道
     */
    private String flowChannel;
    /**
     * 会员权益购买状态
     */
    private String packageStatus;
    /**
     * 资金方
     */
    private String bankChannel;

}
