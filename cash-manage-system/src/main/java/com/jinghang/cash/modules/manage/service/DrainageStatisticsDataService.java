package com.jinghang.cash.modules.manage.service;

import com.github.pagehelper.Page;
import com.jinghang.cash.modules.manage.vo.req.DrainageStatisticsRequest;
import com.jinghang.cash.pojo.DrainageStatistics;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR> gale
 * @Classname DrainageStatisticsDataService
 * @Description 引流统计服务
 * @Date 2023/11/20 16:56
 */

public interface DrainageStatisticsDataService {

    Page<DrainageStatistics> queryAll(DrainageStatisticsRequest request);


    void download(List<DrainageStatistics> request, HttpServletResponse response) throws IOException;


}
