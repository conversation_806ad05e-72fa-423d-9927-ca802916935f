package com.jinghang.cash.modules.manage.vo.res;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.jinghang.cash.enums.ProcessState;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 销账记录
 * <AUTHOR>
 */
@Data
public class OfflineRepayApplyResponse implements Serializable {
    private static final long serialVersionUID = 1417951300311386620L;
    /**
     * 标识id
     */
    private String id;
    /**
     * 订单编号
     */
    private String orderId;
    /**
     * 还款状态
     */
    private ProcessState applyState;
    /**
     * 还款模式
     */
    private String repayPurpose;
    /**
     * 应还金额
     */
    private BigDecimal amount;
    /**
     * 实际还款金额
     */
    private BigDecimal actAmount;

    /**
     * 减免金额
     */
    private BigDecimal reduceAmount;
    /**
     * 创建人
     */
    private String createdBy;
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createdTime;
    /**
     * 更新人
     */
    private String updatedBy;
    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updatedTime;
    /**
     * 溢出金额
     */
    private BigDecimal overflowAmount;
}
