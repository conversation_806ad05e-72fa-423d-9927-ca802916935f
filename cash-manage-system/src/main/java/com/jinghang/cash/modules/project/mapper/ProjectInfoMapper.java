package com.jinghang.cash.modules.project.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jinghang.cash.modules.project.domain.ProjectInfo;
import com.jinghang.cash.modules.project.domain.dto.ProjectInfoQueryCriteria;
import com.jinghang.cash.modules.project.service.domain.vo.ProjectInfoVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 项目信息Mapper
 *
 * @Author: Lior
 * @CreateTime: 2025/8/20 13:40
 */
@Mapper
public interface ProjectInfoMapper extends BaseMapper<ProjectInfo> {

    /**
     * 根据项目编码查询项目信息
     *
     * @param projectCode 项目编码
     * @return 项目信息
     */
    ProjectInfo selectByProjectCode(@Param("projectCode") String projectCode);

    /**
     * 查询所有启用状态的项目信息
     *
     * @return 启用状态的项目信息列表
     */
    List<ProjectInfo> selectAllEnabledProjects();


    Page<ProjectInfoVo> queryProjectInfoPage(@Param("page") Page<ProjectInfoVo> page, @Param("param") ProjectInfoQueryCriteria param);

    Page<ProjectInfoVo> queryTemProjectInfoPage(@Param("page") Page<ProjectInfoVo> page, @Param("param") ProjectInfoQueryCriteria param);



}
