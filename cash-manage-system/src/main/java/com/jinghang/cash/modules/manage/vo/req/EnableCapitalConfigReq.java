package com.jinghang.cash.modules.manage.vo.req;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 资金配置启用禁用
 *
 * <AUTHOR>
 */
@Data
public class EnableCapitalConfigReq implements Serializable {
    private static final long serialVersionUID = -1905170501608989288L;

    @NotBlank(message = "资金配置id不能为空")
    private String id;

    /**
     * 启用状态
     */
    @NotBlank(message = "状态不能为空")
    private String enabled;
}
