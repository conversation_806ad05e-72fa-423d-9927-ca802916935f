package com.jinghang.cash.modules.manage.vo.req;

import com.jinghang.cash.enums.RepayPurpose;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
public class ReduceApplyReq {

    /**
     * 还款方式 结清 或者当期
     */
    @NotNull
    private RepayPurpose repayPurpose;

    /**
     * 期数
     */
    private Integer period;

    /**
     * 订单id
     */
    @NotNull
    private String orderId;

    /**
     * 减免金额
     */
    @NotNull
    private BigDecimal reduceAmount;

    /**
     * 减免备注
     */
    private String remark;

}
