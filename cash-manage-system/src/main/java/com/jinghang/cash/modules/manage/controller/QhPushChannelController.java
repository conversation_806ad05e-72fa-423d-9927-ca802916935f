package com.jinghang.cash.modules.manage.controller;

import com.github.pagehelper.PageInfo;
import com.jinghang.cash.enums.ResultCode;
import com.jinghang.cash.modules.manage.RestResult;
import com.jinghang.cash.modules.manage.service.impl.QhChannelService;
import com.jinghang.cash.modules.manage.vo.req.QhChannelQueryRequest;
import com.jinghang.cash.modules.manage.vo.req.QhChannelRequest;
import com.jinghang.cash.modules.manage.vo.req.QhChannelUpdateRequest;
import com.jinghang.cash.modules.manage.vo.rsp.AppChannelConfigResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * <AUTHOR> gale
 * @Classname QhPushChannelController
 * @Description app渠道
 * @Date 2024/3/23 17:30
 */
@RestController
@RequestMapping("app/channel")
public class QhPushChannelController {

    Logger logger = LoggerFactory.getLogger(QhPushChannelController.class);

    @Autowired
    private QhChannelService qhChannelService;

    //新增渠道
    @PostMapping(value = "/add")
    public RestResult<String> add(@Validated @RequestBody QhChannelRequest request) {
        ResultCode resultCode;
        try {
            resultCode = qhChannelService.add(request);
        } catch (Exception e) {
            logger.error("新增渠道异常:", e);
            return RestResult.fail(ResultCode.BIZ_ERROR);
        }
        return RestResult.create(resultCode);
    }

    //修改渠道
    @PostMapping(value = "/update")
    public RestResult<String> update(@Validated @RequestBody QhChannelUpdateRequest request) {
        ResultCode resultCode;
        try {
            resultCode = qhChannelService.update(request);
        } catch (Exception e) {
            return RestResult.fail(ResultCode.BIZ_ERROR);
        }
        return RestResult.create(resultCode);
    }
    @PostMapping("/query")
    public RestResult<PageInfo<AppChannelConfigResponse>> query(@RequestBody QhChannelQueryRequest request) {
        PageInfo<AppChannelConfigResponse> channelList;
        try {
            channelList = qhChannelService.queryChannelList(request.getPageNum(), request.getPageSize());
        } catch (Exception e) {
            return RestResult.fail(ResultCode.BIZ_ERROR);
        }
        return RestResult.success(channelList);
    }

}
