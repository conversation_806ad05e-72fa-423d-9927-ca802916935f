<template>
  <div class="app-container">
    <div style="margin-bottom: 10px;">
      <el-button v-permission="['admin','channel:add']" round type="primary" size="mini" @click="open">新增</el-button>
    </div>

    <el-table border="border" :data="list" v-loading="loading">
      <el-table-column label="序号" type="index" align="center">
        <template slot-scope="scope">
          <span
            :style="{ opacity: scope.row.enabled === 'DISABLE' ? '0.3' : 1 }"
            >{{ parseInt(scope.$index) + 1 }}</span
          >
        </template>
      </el-table-column>
      <el-table-column label="资方" prop="bankChannel" align="center">
        <template slot-scope="scope">
          <!-- <span :style="{ opacity: scope.row.enabled === 'DISABLE' ? '0.3' : 1 }">{{ bankChannelFormat(scope.row) }}</span> -->
          <span
            :style="{ opacity: scope.row.enabled === 'DISABLE' ? '0.3' : 1 }"
            >{{ scope.row.desc }}</span
          >
        </template>
      </el-table-column>
      <el-table-column label="资方编码" prop="bankChannel" align="center">
        <template slot-scope="scope">
          <span
            :style="{ opacity: scope.row.enabled === 'DISABLE' ? '0.3' : 1 }"
            >{{ scope.row.bankChannel }}</span
          >
        </template>
      </el-table-column>
      <el-table-column
        label="日授信限额/万元"
        prop="creditDayLimit"
        align="center"
      >
        <template slot-scope="scope">
          <span
            :style="{ opacity: scope.row.enabled === 'DISABLE' ? '0.3' : 1 }"
            >{{ scope.row.creditDayLimit }}</span
          >
        </template>
      </el-table-column>
      <el-table-column
        label="日放款限额/万元"
        prop="loanDayLimit"
        align="center"
      >
        <template slot-scope="scope">
          <span
            :style="{ opacity: scope.row.enabled === 'DISABLE' ? '0.3' : 1 }"
            >{{ scope.row.loanDayLimit }}</span
          >
        </template>
      </el-table-column>
      <el-table-column label="借款期限" prop="periodsRange" align="center">
        <template slot-scope="scope">
          <span
            :style="{ opacity: scope.row.enabled === 'DISABLE' ? '0.3' : 1 }"
            >{{ scope.row.periodsRange }}</span
          >
        </template>
      </el-table-column>
      <el-table-column label="修改时间" prop="updatedTime" align="center">
        <template slot-scope="scope">
          <span
            :style="{ opacity: scope.row.enabled === 'DISABLE' ? '0.3' : 1 }"
            >{{ scope.row.updatedTime }}</span
          >
        </template>
      </el-table-column>
      <el-table-column label="修改人" prop="updatedBy" align="center">
        <template slot-scope="scope">
          <span
            :style="{ opacity: scope.row.enabled === 'DISABLE' ? '0.3' : 1 }"
            >{{ scope.row.updatedBy }}</span
          >
        </template>
      </el-table-column>
      <el-table-column label="操作" fixed="right" width="150" align="center">
        <template slot-scope="scope">
          <el-button
            v-permission="['admin','channel:view']"
            style="padding: 0;"
            type="text"
            size="mini"
            @click="detail(scope.row.id)"
            >查看</el-button
          >
          <el-button
            v-permission="['admin','channel:edite']"
            style="padding: 0;"
            type="text"
            size="mini"
            @click="edite(scope.row.id)"
            >修改</el-button
          >
          <el-button
            v-permission="['admin','channel:open']"
            style="padding: 0;"
            v-if="scope.row.enabled === 'DISABLE'"
            type="text"
            size="mini"
            @click="toggle(scope.row.id, 'ENABLE')"
            >启用</el-button
          >
          <el-button
            v-permission="['admin','channel:stop']"
            style="padding: 0; color: #c00;"
            v-if="scope.row.enabled === 'ENABLE'"
            type="text"
            size="mini"
            @click="toggle(scope.row.id, 'DISABLE')"
            >停用</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 新增 -->
    <el-dialog
      :title="title"
      :visible.sync="visible"
      width="670px"
      :before-close="close"
    >
      <el-form
        label-position="left"
        :model="ruleForm"
        :rules="rules"
        ref="ruleForm"
        label-width="100px"
      >
        <el-form-item label="资方" prop="bankChannel" v-if="mode === 'edite'">
          <el-select
            placeholder="请选择"
            style="width:300px"
            v-model="ruleForm.bankChannel"
            :disabled="mode === 'detail'"
          >
            <el-option
              v-for="item in ruleForm.bankChannelList"
              :label="item.desc"
              :value="item.name"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="资方" prop="bankChannel" v-if="mode !== 'edite'">
          <el-select
            placeholder="请选择"
            style="width:300px"
            v-model="ruleForm.bankChannel"
            @change="bankChannelChange"
            :disabled="mode === 'detail'"
          >
            <el-option
              v-for="item in bankChannelOptions"
              :label="item.desc"
              :value="item.name"
            ></el-option>
          </el-select>
          <div v-if="isExist" style="color: #c00; font-size: 12px;">资方已存在</div>
        </el-form-item>

        <el-form-item label="融担公司" prop="guaranteeCompany">
          <el-select
            placeholder="请选择"
            style="width:300px"
            v-model="ruleForm.guaranteeCompany"
            :disabled="mode === 'detail' || mode === 'edite'"
          >
            <el-option
              v-for="item in guaranteeCompanyOptions"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="资方利率" prop="bankRate">
          <el-input
            style="width:300px; margin-right: 10px;"
            v-model="ruleForm.bankRate"
            @input="checkNumber"
            :disabled="mode === 'detail'"
          ></el-input
          >%
        </el-form-item>
        <el-form-item label="借款期限" prop="periodsRange">
          <el-checkbox-group v-model="ruleForm.periodsRange">
            <el-checkbox
              v-for="item in periodsRangeOptions"
              :label="item.label"
              :disabled="mode === 'detail'"
            ></el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item label="支持利率" prop="supportIrrLevel">
          <el-checkbox-group v-model="ruleForm.supportIrrLevel">
            <el-checkbox
              v-for="item in supportIrrLevelOptions"
              :label="item.label"
              :disabled="mode === 'detail'"
            ></el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item label="单笔限额" required>
          <div style="display: flex; align-items: center;">
            <el-form-item prop="singleAmtRange1">
              <el-input
                @input="checkNumber"
                style="width:180px;"
                v-model="ruleForm.singleAmtRange1"
                :disabled="mode === 'detail'"
              />
            </el-form-item>
            <span style="padding: 0 10px; position: relative; top: -10px;"
              >-</span
            >
            <el-form-item prop="singleAmtRange2">
              <el-input
                @input="checkNumber"
                style="width:180px;"
                v-model="ruleForm.singleAmtRange2"
                :disabled="mode === 'detail'"
              ></el-input>
            </el-form-item>
            <span style="padding: 0 10px; position: relative; top: -10px;"
              >元</span
            >
          </div>
        </el-form-item>
        <el-form-item label="年龄范围" required>
          <div style="display: flex; align-items: center;">
            <el-form-item prop="agesRange1">
              <el-input
                @input="checkNumberAge"
                style="width:180px;"
                v-model="ruleForm.agesRange1"
                :disabled="mode === 'detail'"
              ></el-input>
            </el-form-item>
            <span style="padding: 0 10px; position: relative; top: -10px;"
              >-</span
            >
            <el-form-item prop="agesRange2">
              <el-input
                @input="checkNumberAge"
                style="width:180px;"
                v-model="ruleForm.agesRange2"
                :disabled="mode === 'detail'"
              ></el-input>
            </el-form-item>
            <span style="padding: 0 10px; position: relative; top: -10px;"
              >岁</span
            >
          </div>
        </el-form-item>
        <el-form-item label="日授信额上限" prop="creditDayLimit">
          <el-input
            @input="checkNumber"
            style="width:300px; margin-right: 10px;"
            v-model="ruleForm.creditDayLimit"
            :disabled="mode === 'detail'"
          ></el-input
          >万元
        </el-form-item>
        <el-form-item label="日放款额上限" prop="loanDayLimit">
          <el-input
            @input="checkNumber"
            style="width:300px; margin-right: 10px;"
            v-model="ruleForm.loanDayLimit"
            :disabled="mode === 'detail'"
          ></el-input
          >万元
        </el-form-item>
        <el-form-item label="授信允许时间" required>
          <div style="display: flex; align-items: center;">
            <div
              style="position: relative;"
              :style="{ 'top': ruleForm.creditTimeStatus ? '-10px' : '0px' }"
            >
              <el-switch
                :disabled="mode === 'detail'"
                active-color="#1a7efd"
                inactive-color="#ccc"
                v-model="ruleForm.creditTimeStatus"
                style="margin-right: 10px;"
              ></el-switch>
            </div>
            <template v-if="ruleForm.creditTimeStatus">
              <el-form-item prop="creditStartTime">
                <el-time-picker
                  value-format="HH:mm:ss"
                  v-model="ruleForm.creditStartTime"
                  placeholder="开始时间"
                  style="margin-right: 10px;"
                  :disabled="mode === 'detail'"
                ></el-time-picker>
              </el-form-item>
              <el-form-item prop="creditEndTime">
                <el-time-picker
                  value-format="HH:mm:ss"
                  v-model="ruleForm.creditEndTime"
                  placeholder="结束时间"
                  :disabled="mode === 'detail'"
                ></el-time-picker>
              </el-form-item>
            </template>
          </div>
        </el-form-item>
        <el-form-item label="放款允许时间" required>
          <div style="display: flex; align-items: center;">
            <div
              style="position: relative;"
              :style="{ 'top': ruleForm.loanTimeStatus ? '-10px' : '0px' }"
            >
              <el-switch
                :disabled="mode === 'detail'"
                active-color="#1a7efd"
                inactive-color="#ccc"
                v-model="ruleForm.loanTimeStatus"
                style="margin-right: 10px;"
              ></el-switch>
            </div>
            <template v-if="ruleForm.loanTimeStatus">
              <el-form-item prop="loanStartTime">
                <el-time-picker
                  value-format="HH:mm:ss"
                  v-model="ruleForm.loanStartTime"
                  placeholder="开始时间"
                  style="margin-right: 10px;"
                  :disabled="mode === 'detail'"
                ></el-time-picker>
              </el-form-item>
              <el-form-item prop="loanEndTime">
                <el-time-picker
                  value-format="HH:mm:ss"
                  v-model="ruleForm.loanEndTime"
                  placeholder="结束时间"
                  :disabled="mode === 'detail'"
                ></el-time-picker>
              </el-form-item>
            </template>
          </div>
        </el-form-item>
        <el-form-item label="还款允许时间" required>
          <div style="display: flex; align-items: center;">
            <div
              style="position: relative;"
              :style="{ 'top': ruleForm.repayTimeStatus ? '-10px' : '0px' }"
            >
              <el-switch
                :disabled="mode === 'detail'"
                active-color="#1a7efd"
                inactive-color="#ccc"
                v-model="ruleForm.repayTimeStatus"
                style="margin-right: 10px;"
              ></el-switch>
            </div>
            <template v-if="ruleForm.repayTimeStatus">
              <el-form-item prop="repayStartTime">
                <el-time-picker
                  value-format="HH:mm:ss"
                  v-model="ruleForm.repayStartTime"
                  placeholder="开始时间"
                  style="margin-right: 10px;"
                  :disabled="mode === 'detail'"
                ></el-time-picker>
              </el-form-item>
              <el-form-item prop="repayEndTime">
                <el-time-picker
                  value-format="HH:mm:ss"
                  v-model="ruleForm.repayEndTime"
                  placeholder="结束时间"
                  :disabled="mode === 'detail'"
                ></el-time-picker>
              </el-form-item>
            </template>
          </div>
        </el-form-item>

        <el-form-item label="是否允许续借" prop="renewedFlag">
          <el-radio-group v-model="ruleForm.renewedFlag" :disabled="mode === 'detail'">
            <el-radio label="Y">是</el-radio>
            <el-radio label="N">否</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="绑卡渠道" >
          <el-select v-model="ruleForm.protocolChannel" placeholder="绑卡渠道"
            :disabled="mode === 'detail' || mode === 'edite'"
            style="width: 200px;" clearable>
            <el-option
              v-for="item in dict.protocolChannel"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="状态" v-if="mode === 'detail'">
          <span v-if="ruleForm.enabled === 'ENABLE'">启用</span>
          <span v-if="ruleForm.enabled === 'DISABLE'">停用</span>
        </el-form-item>

        <el-form-item
          style="display: flex; justify-content: flex-end; padding-top: 20px;"
          v-if="mode !== 'detail'"
        >
          <el-button round @click="close">取消</el-button>
          <el-button round type="primary" @click="submit">保存</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import {
  getCapitalList,
  saveCapitalConfig,
  queryCapitalConfigPage,
  enableCapitalConfig,
  getCapitalConfig,
  updateCapitalConfig,
  queryCapital
} from "@/api/setting";
import { get as getDictByName } from "@/api/system/dictDetail"

export default {
  name: "",
  data() {
    return {
      queryParams: {
        pageNum: 1,
        pageSize: 10
      },
      title: "新增",
      total: 0,
      loading: false,
      visible: false,
      list: [],
      mode: "",
      ruleForm: {
        enabled: "ENABLE", // 状态
        bankChannel: "", // 资金方
        bankRate: "", // 资方利率
        periodsRange: [], // 支持期数
        supportIrrLevel: [], // 资方支持利率
        creditDayLimit: "", // 资方授信日限额
        loanDayLimit: "", // 资方放款日限额
        agesRange1: "",
        agesRange2: "", // 年龄区间(,号分割)
        singleAmtRange1: "",
        singleAmtRange2: "", // 单笔限额，逗号分割
        creditStartTime: "", // 授信开始时间
        creditEndTime: "", // 授信截止时间
        loanStartTime: "", // 放款开始时间
        loanEndTime: "", // 放款截止时间
        repayStartTime: "", // 还款开始时间
        repayEndTime: "", // 还款结束时间
        creditTimeStatus: true,
        loanTimeStatus: true,
        repayTimeStatus: true
      },
      isExist:false,
      rules: {
        bankChannel: [
          { required: true, message: "资方不能为空", trigger: "blur" }
        ],
        bankRate: [
          { required: true, message: "资方利率不能为空", trigger: "blur" }
        ],
        periodsRange: [
          { required: true, message: "借款期限不能为空", trigger: "blur" }
        ],
        supportIrrLevel: [
          { required: true, message: "支持利率不能为空", trigger: "blur" }
        ],
        singleAmtRange1: [
          { required: true, message: "单笔限额不能为空", trigger: "blur" },
          { validator: this.validateAmount, trigger: "blur" }
        ],
        singleAmtRange2: [
          { required: true, message: "单笔限额不能为空", trigger: "blur" },
          { validator: this.validateAmount, trigger: "blur" }
        ],
        agesRange1: [
          { required: true, message: "年龄范围不能为空", trigger: "blur" }
        ],
        agesRange2: [
          { required: true, message: "年龄范围不能为空", trigger: "blur" }
        ],
        creditDayLimit: [
          { required: true, message: "日授信额上限不能为空", trigger: "blur" },
          { validator: this.validateAmount, trigger: "blur" }
        ],
        loanDayLimit: [
          { required: true, message: "日放款额上限不能为空", trigger: "blur" },
          { validator: this.validateAmount, trigger: "blur" }
        ],
        creditStartTime: [
          { required: true, message: "授信允许时间不能为空", trigger: "blur" }
        ],
        creditEndTime: [
          { required: true, message: "授信允许时间不能为空", trigger: "blur" }
        ],
        repayStartTime: [
          { required: true, message: "还款允许时间不能为空", trigger: "blur" }
        ],
        repayEndTime: [
          { required: true, message: "还款允许时间不能为空", trigger: "blur" }
        ],
        loanStartTime: [
          { required: true, message: "放款允许时间不能为空", trigger: "blur" }
        ],
        loanEndTime: [
          { required: true, message: "放款允许时间不能为空", trigger: "blur" }
        ],
        renewedFlag:[
          { required: true, message: "是否可续借不能为空", trigger: "blur" }
        ],
        guaranteeCompany:[
          { required: true, message: "融担公司不能为空", trigger: "blur" }
        ],

      },
      bankChannelOptions: [],
      periodsRangeOptions: [
        {
          label: "3期",
          value: "3"
        },
        {
          label: "6期",
          value: "6"
        },
        {
          label: "9期",
          value: "9"
        },
        {
          label: "12期",
          value: "12"
        }
      ],
      supportIrrLevelOptions: [
        {
          label: "irr24",
          value: "RATE_24"
        },
        {
          label: "irr36",
          value: "RATE_36"
        }
      ],
      guaranteeCompanyOptions:[],
    };
  },
  dicts: ["protocolChannel"],
  created() {
    getDictByName("guaranteeCompany").then(res => {
      this.guaranteeCompanyOptions = res.content;
    });

    this.getList();
  },

  methods: {
    checkNumber() {
      this.ruleForm.bankRate = this.ruleForm.bankRate.replace(/[^0-9.]/g, "");

      this.ruleForm.singleAmtRange1 = this.ruleForm.singleAmtRange1.replace(
        /[^0-9.]/g,
        ""
      );
      this.ruleForm.singleAmtRange2 = this.ruleForm.singleAmtRange2.replace(
        /[^0-9.]/g,
        ""
      );

      this.ruleForm.creditDayLimit = this.ruleForm.creditDayLimit.replace(
        /[^0-9.]/g,
        ""
      );
      this.ruleForm.loanDayLimit = this.ruleForm.loanDayLimit.replace(
        /[^0-9.]/g,
        ""
      );
    },

    checkNumberAge() {
      this.ruleForm.agesRange1 = this.ruleForm.agesRange1.replace(/\D/g, "");
      this.ruleForm.agesRange2 = this.ruleForm.agesRange2.replace(/\D/g, "");
    },

    validateAmount(rule, value, callback) {
      if (value === "") {
        callback(new Error("请输入金额"));
      } else if (isNaN(value)) {
        callback(new Error("请输入有效的数字"));
      } else if (value > *********) {
        callback(new Error("金额不能超过 *********"));
      } else {
        const decimalRegex = /^\d+(\.\d{1,2})?$/;
        if (!decimalRegex.test(value)) {
          callback(new Error("最多允许两位小数"));
        } else {
          callback();
        }
      }
    },

    toggle(id, type) {
      this.$confirm(
        `是否确认 ${type === "ENABLE" ? "启用" : "停用"} 该资方?`,
        "警告",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          roundButton: true,
          type: "warning"
        }
      ).then(() => {
        enableCapitalConfig({ id, enabled: type }).then(res => {
          this.getList();
        });
      });
    },

    bankChannelChange(e){
      console.log(e)
      const obj = this.bankChannelOptions.find(item => item.name === e)

      if(obj.exist){
        this.isExist = true
      }else{
        this.isExist = false
      }
    },

    // 获取列表
    getList() {
      this.loading = true;
      queryCapitalConfigPage(this.queryParams).then(res => {
        this.list = res.data.list;
        this.total = res.data.total;
        this.loading = false;
      });
    },

    submit() {
      if(this.isExist){
        return
      }
      this.$refs["ruleForm"].validate(valid => {
        if (valid) {

          if (
            Number(this.ruleForm.singleAmtRange1) > Number(this.ruleForm.singleAmtRange2)
          ) {
            this.$message.error("单笔限额范围不正确");
            return;
          }

          if (
            Number(this.ruleForm.agesRange1) > 100 ||
            Number(this.ruleForm.agesRange2) > 100
          ) {
            this.$message.error("年龄不能大于100");
            return;
          }

          if (
            Number(this.ruleForm.agesRange1) > Number(this.ruleForm.agesRange2)
          ) {
            this.$message.error("年龄范围不正确");
            return;
          }


          let periodsRange = this.ruleForm.periodsRange.map(item => {
            const obj = this.periodsRangeOptions.find(x => x.label === item);
            return obj.value;
          });

          let supportIrrLevel = this.ruleForm.supportIrrLevel.map(item => {
            const obj = this.supportIrrLevelOptions.find(x => x.label === item);
            return obj.value;
          });

          let params = {
            ...this.ruleForm,
            periodsRange: periodsRange.join(","),
            supportIrrLevel: supportIrrLevel.join(","),
            agesRange: `${this.ruleForm.agesRange1},${
              this.ruleForm.agesRange2
            }`,
            singleAmtRange: `${this.ruleForm.singleAmtRange1},${
              this.ruleForm.singleAmtRange2
            }`,
            creditTimeStatus: this.ruleForm.creditTimeStatus
              ? "ENABLE"
              : "DISABLE",
            loanTimeStatus: this.ruleForm.loanTimeStatus ? "ENABLE" : "DISABLE",
            repayTimeStatus: this.ruleForm.repayTimeStatus
              ? "ENABLE"
              : "DISABLE"
          };

          delete params.agesRange1;
          delete params.agesRange2;
          delete params.singleAmtRange1;
          delete params.singleAmtRange2;
          delete params.bankChannelList;

          if (params.creditTimeStatus === "DISABLE") {
            delete params.creditStartTime;
            delete params.creditEndTime;
          }

          if (params.loanTimeStatus === "DISABLE") {
            delete params.loanStartTime;
            delete params.loanEndTime;
          }

          if (params.repayTimeStatus === "DISABLE") {
            delete params.repayStartTime;
            delete params.repayEndTime;
          }

          console.log(params);

          if (this.mode === "create") {
            saveCapitalConfig(params).then(res => {
              this.close();
              this.getList();
            });
          } else if (this.mode === "edite") {
            updateCapitalConfig(params).then(res => {
              this.close();
              this.getList();
            });
          }
        }
      });
    },

    // 新增弹窗
    open() {
      queryCapital().then(res => {
        this.bankChannelOptions = res.data;
        this.visible = true;
        this.title = "新增";
        this.mode = "create";
      });
    },

    // 关闭
    close() {
      this.visible = false;
      this.mode = "";
      this.title = "新增";
      this.$refs["ruleForm"].resetFields();
      this.ruleForm = {
        enabled: "ENABLE", // 状态
        bankChannel: "", // 资金方
        bankRate: "", // 资方利率
        periodsRange: [], // 支持期数
        supportIrrLevel: [], // 资方支持利率
        creditDayLimit: "", // 资方授信日限额
        loanDayLimit: "", // 资方放款日限额
        agesRange1: "",
        agesRange2: "", // 年龄区间(,号分割)
        singleAmtRange1: "",
        singleAmtRange2: "", // 单笔限额，逗号分割
        creditStartTime: "", // 授信开始时间
        creditEndTime: "", // 授信截止时间
        loanStartTime: "", // 放款开始时间
        loanEndTime: "", // 放款截止时间
        repayStartTime: "", // 还款开始时间
        repayEndTime: "", // 还款结束时间
        creditTimeStatus: true,
        loanTimeStatus: true,
        repayTimeStatus: true
      };
      this.isExist = false
    },

    detail(id) {
      getCapitalConfig({ id }).then(res => {
        let data = res.data;

        let periodsRange = data.periodsRange.split(",").map(item => {
          const obj = this.periodsRangeOptions.find(x => x.value === item);
          return obj.label;
        });

        let supportIrrLevel = data.supportIrrLevel.split(",").map(item => {
          const obj = this.supportIrrLevelOptions.find(x => x.value === item);
          return obj.label;
        });

        this.ruleForm = {
          ...data,
          periodsRange, // 支持期数
          supportIrrLevel, // 资方支持利率
          agesRange1: data.agesRange.split(",")[0],
          agesRange2: data.agesRange.split(",")[1], // 年龄区间
          singleAmtRange1: data.singleAmtRange.split(",")[0],
          singleAmtRange2: data.singleAmtRange.split(",")[1], // 单笔限额
          creditTimeStatus: data.creditTimeStatus === "ENABLE",
          loanTimeStatus: data.loanTimeStatus === "ENABLE",
          repayTimeStatus: data.repayTimeStatus === "ENABLE"
        };

        this.mode = "detail";
        this.visible = true;
        this.title = "查看";
      });
    },

    edite(id) {
      getCapitalConfig({ id }).then(res => {
        let data = res.data;

        let periodsRange = data.periodsRange.split(",").map(item => {
          const obj = this.periodsRangeOptions.find(x => x.value === item);
          return obj.label;
        });

        let supportIrrLevel = data.supportIrrLevel.split(",").map(item => {
          const obj = this.supportIrrLevelOptions.find(x => x.value === item);
          return obj.label;
        });

        this.ruleForm = {
          ...data,
          periodsRange, // 支持期数
          supportIrrLevel, // 资方支持利率
          agesRange1: data.agesRange.split(",")[0],
          agesRange2: data.agesRange.split(",")[1], // 年龄区间
          singleAmtRange1: data.singleAmtRange.split(",")[0],
          singleAmtRange2: data.singleAmtRange.split(",")[1], // 单笔限额
          creditTimeStatus: data.creditTimeStatus === "ENABLE",
          loanTimeStatus: data.loanTimeStatus === "ENABLE",
          repayTimeStatus: data.repayTimeStatus === "ENABLE"
        };

        this.mode = "edite";
        this.visible = true;
        this.title = "修改";
      });
    },

    bankChannelFormat({ bankChannel }) {
      let obj = this.bankChannelOptions.find(item => item.name === bankChannel);
      return obj ? obj.desc : "--";
    }
  }
};
</script>
