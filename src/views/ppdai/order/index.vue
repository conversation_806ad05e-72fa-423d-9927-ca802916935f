<template>
  <div class="app-container">
    <!-- 搜索表单 -->
    <el-form
      ref="queryForm"
      :model="queryParams"
      :inline="true"
    >
      <el-form-item>
        <el-select
          v-model="queryParams.type"
          placeholder="请选择"
          style="width: 100px;"
        >
          <el-option
            label="手机号"
            value="mobile"
          />
          <el-option
            label="订单编号"
            value="loanId"
          />
          <el-option
            label="身份证号"
            value="certNo"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-input
          v-model="queryParams.typeText"
          placeholder="请输入"
          clearable
          style="width:200px;"
          @keydown.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-select
          v-model="queryParams.flowChannel"
          placeholder="进件渠道"
          style="width: 160px;"
          clearable
        >
          <el-option
            v-for="item in dict.flowChannel"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-date-picker
          v-model="dataVal"
          style="width: 300px"
          value-format="yyyy-MM-dd HH:mm:ss"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="['00:00:00', '23:59:59']"
          :picker-options="pickerOptionsDefault"
        />
      </el-form-item>

      <el-form-item>
        <el-button
          round
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
        >
          查询
        </el-button>
      </el-form-item>
    </el-form>
    <!-- 列表 -->
    <el-table
      v-loading="loading"
      border="border"
      :data="list"
    >
      <el-table-column
        label="订单编号"
        align="center"
        fixed
        prop="loanId"
        width="280px"
      ></el-table-column>
      <el-table-column
        label="借款本金"
        prop="principalAmt"
        align="center"
        width="110px"
      />
      <el-table-column
        label="还款期数"
        prop="period"
        align="center"
        width="60px"
      />
      <el-table-column
        label="减免金额"
        prop="reduceAmount"
        align="center"
      />
      <el-table-column
        label="还款模式"
        align="center"
      >
        <template slot-scope="scope">
          <dict-tag
            :value="scope.row.repayPurpose"
            :options="dict.repayPurpose"
          />
        </template>
      </el-table-column>
      <el-table-column
        label="使用状态"
        align="center"
      >
        <template slot-scope="scope">
          <dict-tag
            :value="scope.row.useState"
            :options="dict.useState"
          />
        </template>
      </el-table-column>
      <el-table-column
        label="审核状态"
        align="center"
      >
        <template slot-scope="scope">
          <dict-tag
            :value="scope.row.auditState"
            :options="dict.auditState"
          />
        </template>
      </el-table-column>
      <el-table-column
        label="创建人"
        prop="createdBy"
        align="center"
      />
      <el-table-column
        label="审批人"
        prop="updatedBy"
        align="center"
        width="120px"
      />
      <el-table-column
        label="备注"
        prop="remark"
        align="center"
        min-width="160px"
      />
      <el-table-column
        label="创建时间"
        prop="createdTime"
        align="center"
        width="160px"
      />
      <el-table-column
        label="更新时间"
        prop="updatedTime"
        align="center"
        width="160px"
      />
      <!-- <el-table-column label="结清证明">
        <template slot-scope="scope">
          <el-button
            v-if="scope.row.isClear === 'Y'"
            v-permission="['admin','order:download']"
            type="text"
            @click="handleDownload(scope.row, 'CREDIT_SETTLE_VOUCHER_FILE')"
          >
            下载
          </el-button>
        </template>
      </el-table-column>

      <el-table-column label="放款凭证">
        <template slot-scope="scope">
          <el-button
            v-if="scope.row.isLoanState === 'Y'"
            v-permission="['admin','order:download']"
            type="text"
            @click="handleDownload(scope.row, 'LOAN_VOUCHER_FILE')"
          >
            下载
          </el-button>
        </template>
      </el-table-column> -->


      <el-table-column
        label="操作"
        fixed="right"
        width="160px"
      >
        <template v-if="showButton(scope.row)" slot-scope="scope">
          <el-button
            v-permission="['admin','ppdai:examine']"
            type="text"
            @click="examine(scope.row, 'PASS')"
          >
            审核通过
          </el-button>
          <el-button
            v-permission="['admin','ppdai:examine']"
            type="text"
            @click="examine(scope.row, 'REFUSE')"
          >
            审核拒绝
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import {
  orderList,
  downloadVoucherFile
} from "@/api/order";
import {
  queryReducePage, // 减免订单列表
  reduceAudit, // 减免审核
} from "@/api/ppdai";
import crud from '@/mixins/crud'
let minTime = null
export default {
  name: "PpdaiOrder",
  mixins: [crud],
  data() {
    return {
      // 查询参数
      queryParams: {
        type: "mobile",
        typeText: "",
        pageNum: 1,
        pageSize: 10,
        flowChannel: undefined,
        startTime: undefined,
        endTime: undefined
      },
      loading: false,
      list: [],
      total: 0,
      dataVal: this.FormDataValue(),
      pickData: {},
      pickerOptionsDefault: {
        onPick: pick => {
          this.pickData = pick
        },
        disabledDate: time => {
          //设置当前时间后的时间不可选
          const now = new Date()
          const year = now.getFullYear()
          const mouth = now.getMonth() + 1
          const day = now.getDate()
          const {minDate, maxDate} = this.pickData
          const after =  time.valueOf() > new Date(`${year}-${mouth}-${day} 23:59:59`) // 是否今天之后
          // 如果选择了一个时间
          if (minDate && !maxDate) {
            const interval = Math.abs(minDate.valueOf() - time.valueOf())
            const out31 = interval > 1000 * 3600 * 24 * 31 // 是否31天之外
            return out31 || after;
          }
          return after;
        }
      },
    };
  },
  dicts: ["flowChannel", 'useState', 'repayPurpose', 'auditState'],
  created() {
    this.getList()
  },
  methods: {
    // 搜索
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },

    // 获取订单列表
    getList() {
      let params = {
        pageNum: this.queryParams.pageNum,
        pageSize: this.queryParams.pageSize,
        flowChannel: this.queryParams.flowChannel
      };

      if (this.queryParams.typeText) {
        params[this.queryParams.type] = this.queryParams.typeText
      }

      if(this.dataVal && this.dataVal.length === 2){
        params.startTime = this.dataVal[0];
        params.endTime =  this.dataVal[1];
        // 判断时间差是否大于31天
        if (this.checkInterval()) return

      }else{
        params.startTime = undefined
        params.endTime = undefined
      }
      this.loading = true;
      queryReducePage(params).then(res => {
        this.list = res.data.list;
        this.total = res.data.total;
        this.loading = false;
      }, err => {
        console.log(err)
        this.loading = false
        this.$message.error('查询失败,请稍后再试!')
      });
    },

    FormDataValue() {
      const start = new Date().getTime() - 3600 * 1000 * 24 * 30
      const end = new Date()
      const startDate = this.parseTime(start, '{y}-{m}-{d}')
      const endDate = this.parseTime(end, '{y}-{m}-{d}')

      return [`${startDate} 00:00:00`, `${endDate} 23:59:59`]
    },
    // 是否显示按钮
    showButton(row) {
      // 审核通过或拒绝以及使用状态已过期的不显示按钮
      if (['PASS', 'REFUSE'].includes(row.auditState) || row.useState === 'EXPIRED') {
        return false;
      } else return true;
    },
    // 检查日期间隔
    checkInterval() {
      const interval = new Date(this.dataVal[1]).getTime() - new Date(this.dataVal[0]).getTime();
      if (interval > 0 && interval > (1000 * 60 * 60 * 24 * 31)){
        this.$message.error('请选择31天内的日期!')
        return true
      }
    },

    // 审核
    examine(row, auditState) {
      this.$confirm("是否确认审核该订单?", "温馨提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        roundButton: true,
        type: "warning"
      }).then(() => {
        this.loading = true;
        let params = {
          reduceId: row.reduceId,
          auditState
        }
        reduceAudit(params).then(() => {
          this.$message.success('审核完成！')
          this.getList()
        }, err => {
          console.log(err)
          this.loading = false
          this.$message.error('审核失败,请稍后再试!')
        })
      })
    },
    // 下载
    handleDownload(row, type = 'CREDIT_SETTLE_VOUCHER_FILE') {
      let params = {
        loanId: row.loanId,
        bankChannel: row.bankChannel,
        type
      }
      downloadFile(params).then(res => {
        const { fileStatus } = res.data

        if(fileStatus === 'PROCESSING'){
          this.$message.error('已请求资方结清证明，请1小时后查看')
        }else if(fileStatus === 'SUCCESS'){
          window.open(res.data.fileUrl)
        }
      }, err => {
        console.log(err)
        this.$message.error('下载失败,请稍后再试!')
      })
    }

  }
};
</script>

<style scoped>
.item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: #e5e5e5 1px solid;
  padding-bottom: 10px;
  margin-bottom: 10px;
}

.item span {
  font-size: 16px;
}
</style>
