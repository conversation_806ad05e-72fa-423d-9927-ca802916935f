<template>
  <div class="app-container">
    <el-form
      ref="queryForm"
      :model="queryParams"
      :inline="true"
    >
      <el-form-item
        label=""
        prop="orderId"
        width="150px"
      >
        <el-input
          v-model="queryParams.orderId"
          placeholder="订单编号"
          clearable
          size="small"
        />
      </el-form-item>
      <el-form-item
        label=""
        prop="rightsSupplier"
      >
        <el-input
          v-model="queryParams.rightsSupplier"
          placeholder="供应商编号"
          clearable
          size="small"
        />
      </el-form-item>
      <el-form-item
        label=""
        prop="payNo"
      >
        <el-input
          v-model="queryParams.payNo"
          placeholder="支付单号"
          clearable
          size="small"
        />
      </el-form-item>
      <el-form-item
        label=""
        prop="userId"
        width="150px"
      >
        <el-input
          v-model="queryParams.userId"
          placeholder="用户编号"
          clearable
          size="small"
        />
      </el-form-item>
      <el-form-item
        label=""
        prop="mobile"
        width="150px"
      >
        <el-input
          v-model="queryParams.mobile"
          placeholder="输入手机号"
          clearable
          size="small"
        />
      </el-form-item>
      <el-form-item
        label=""
        prop="repayState"
      >
        <el-select
          v-model="queryParams.repayState"
          placeholder="订单状态"
          style="width: 110px"
          clearable
        >
          <el-option
            v-for="item in repayStateOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item
        label=""
        prop="repayMode"
      >
        <el-select
          v-model="queryParams.repayMode"
          placeholder="支付方式"
          style="width: 110px"
          clearable
        />
      </el-form-item>
      <el-form-item
        label="支付时间"
        prop="dataVal"
      >
        <el-date-picker
          v-model="dataVal"
          size="small"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :picker-options="pickerOptionsDefault"
          clearable
        />
      </el-form-item>
      <el-form-item
        label="退款时间"
        prop="dataVal1"
      >
        <el-date-picker
          v-model="dataVal1"
          size="small"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :picker-options="pickerOptionsDefault"
          clearable
        />
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          round
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
        >
          搜索
        </el-button>
        <el-button
          v-permission="['admin', 'pay:export']"
          type="success"
          round
          size="mini"
          @click="exportDetail"
        >
          导出
        </el-button>
      </el-form-item>
    </el-form>

    <div style="margin-bottom: 16px;">
      <span style="margin-right: 24px;">
        权益金额：{{ rightsAmount }} 元
      </span>
      <span style="margin-right: 24px;">
        退款金额：{{ refundAmount }} 元
      </span>
    </div>

    <el-table
      v-loading="loading"
      border="border"
      :data="list"
    >
      <el-table-column
        label="会员权益单号"
        prop="rightsPackageId"
        align="center"
      />
      <el-table-column
        label="支付单号"
        prop="payNo"
        align="center"
      />
      <el-table-column
        label="业务单号"
        prop="loanId"
        align="center"
      />
      <el-table-column
        label="姓名"
        prop="name"
        align="center"
      />
      <el-table-column
        label="手机号"
        prop="mobile"
        align="center"
      />
      <el-table-column
        label="会员供应商"
        prop="rightsSupplier"
        align="center"
      />
      <el-table-column
        label="权益类型"
        prop="code"
        align="center"
      />
      <el-table-column
        label="金额"
        prop="totalAmt"
        align="center"
      />
      <el-table-column
        label="实际金额"
        prop="rightsActualAmount"
        align="center"
      />
      <el-table-column
        label="支付方式"
        prop="repayMode"
        align="center"
      />
      <el-table-column
        label="放款时间"
        prop="loanTime"
        align="center"
      />
      <el-table-column
        label="应付款时间"
        prop="planRepayDate"
        align="center"
      />
      <el-table-column
        label="支付时间"
        prop="payTime"
        align="center"
      />
      <el-table-column
        label="订单状态"
        prop="repayState"
        align="center"
        :formatter="repayStateFormat"
      />
      <el-table-column
        label="退款金额"
        prop="refundAmount"
        align="center"
      />
      <el-table-column
        label="退款时间"
        prop="refundTime"
        align="center"
        width="100px"
      />
      <el-table-column
        label="操作"
        align="center"
        fixed="right"
      >
        <template slot-scope="scope">
          <el-button
            v-if="scope.row.repayState === 'SUCCEED'"
            v-permission="['admin', 'pay:refund']"
            type="text"
            @click="handleRefund(scope.row)"
          >
            退款
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import { rightsQuery, rightsDownload, refund } from '@/api/rights'
import { get as getDictByName } from '@/api/system/dictDetail'
import fileDownload from 'js-file-download'

function formatDate(date) {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0'); // 处理月份为两位数
  const day = String(date.getDate()).padStart(2, '0'); // 处理日期为两位数
  return `${year}-${month}-${day}`;
}

function isSameMonth(date1, date2) {
  if (!date1 && !date2) return true;

  const d1 = new Date(date1);
  const d2 = new Date(date2);

  // 获取年份和月份
  const year1 = d1.getFullYear();
  const month1 = d1.getMonth() + 1;

  const year2 = d2.getFullYear();
  const month2 = d2.getMonth() + 1;

  // 判断年份和月份是否相同
  return year1 === year2 && month1 === month2;
}

export default {
  name: '',
  data() {
    return {
      loading: false,
      total: 0,
      repayStateOptions: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10
      },
      list: [],
      refundAmount: null,
      rightsAmount: null,
      dataVal: [],
      dataVal1: [],
      pickerOptionsDefault: {
        onPick: ({ maxDate, minDate }) => {
          // 当我们选择两个值的时候，就认为用户已经选择完毕
          if (maxDate != null && minDate != null) {
            this.repayDateBegin = maxDate
            this.repayDateEnd = minDate
          }
        },
        disabledDate: (time) => {
          const maxDate = this.maxDate
          const minDate = this.minDate
          if (maxDate != null && minDate != null) {
            const days = maxDate.getTime() - minDate.getTime()
            // 计算完之后必须清除，否则选择器一直处于禁止选择的状态
            this.maxDate = null
            this.minDate = null
            return parseInt(days / (1000 * 60 * 60 * 24)) > 30
          } else {
            // 设置当前时间后的时间不可选
            return time.getTime() > Date.now()
          }
        }
      }
    }
  },
  created() {
    getDictByName('repayState').then(res => {
      this.repayStateOptions = res.content
    })

    this.getList()
  },
  methods: {
    /** 查询参数列表 */
    getList() {
      this.loading = true
      if (this.dataVal && this.dataVal.length === 2) {
        this.queryParams.startDate = this.dataVal[0]
        this.queryParams.endDate = this.dataVal[1]
      } else {
        this.queryParams.startDate = undefined
        this.queryParams.endDate = undefined
      }

      if (this.dataVal1 && this.dataVal1.length === 2) {
        this.queryParams.refundStartDate = this.dataVal1[0]
        this.queryParams.refundEndDate = this.dataVal1[1]
      } else {
        this.queryParams.refundStartDate = undefined
        this.queryParams.refundEndDate = undefined
      }


      rightsQuery(this.queryParams).then((res) => {
        this.refundAmount = res.data.refundAmount
        this.rightsAmount = res.data.rightsAmount
        this.list = res.data.pageInfo.list
        this.total = res.data.pageInfo.total
        this.loading = false
      })
    },

    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },

    // 导出
    exportDetail() {
      this.$confirm('是否确认导出所有数据项?', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        roundButton: true,
        type: 'warning'
      }).then(() => {
        if (isSameMonth(this.queryParams.startDate, this.queryParams.endDate)) {
          const firstDay = new Date(new Date().getFullYear(), new Date().getMonth(), 1);
          const lastDay = new Date(new Date().getFullYear(), new Date().getMonth() + 1, 0);

          const params = {
            ...this.queryParams,
            pageNum: undefined,
            pageSize: undefined,
            startDate: this.queryParams.startDate ? this.queryParams.startDate : formatDate(firstDay),
            endDate: this.queryParams.endDate ? this.queryParams.endDate : formatDate(lastDay),
          }

          rightsDownload(params).then(res => {
            fileDownload(res, '支付数据.xlsx')
          })
        } else {
          this.$message({
            message: '单次导出数据范围限制为一个月内，请重新选择',
            type: 'warning'
          })
        }
      })
    },

    repayStateFormat({ repayState }) {
      const obj = this.repayStateOptions.find(item => item.value === repayState)
      return obj ? obj.label : '--'
    },

    // 退款操作
    handleRefund(row) {
      this.$prompt('请输入退款金额（整数）', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputPattern: /^[1-9]\d*$/,
        inputErrorMessage: '金额为正整数',
        beforeClose: (action, instance, done) => {
          if (action === 'confirm') {
            // 执行你的逻辑判断
            if (instance.inputValue > row.totalAmt) {
              this.$message.error('退款金额不能大于支付金额')
              return false
            }
            instance.confirmButtonLoading = true
            refund({
              orderId: row.id,
              refundAmount: Number(instance.inputValue)
            }).then(res => {
              this.getList()
              instance.confirmButtonLoading = false
              done()
            }).catch(() => {
              instance.confirmButtonLoading = false
              done()
            })
          } else {
            done() // 关闭对话框
          }
        }
      })
    }
  }
}
</script>
