<template>
  <div class="app-container">
    <el-form
      ref="queryForm"
      :model="queryParams"
      :inline="true"
    >
      <el-form-item
        label="手机号码"
        prop="mobile"
        width="150px"
      >
        <el-input
          v-model="queryParams.mobile"
          placeholder="请输入"
          clearable
          size="small"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="添加时间">
        <el-date-picker
          v-model="dataVal"
          size="small"
          type="datetimerange"
          clearable
          style="width: 380px"
          value-format="yyyy-MM-dd HH:mm:ss"
          range-separator="-"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          :default-time="['00:00:00', '23:59:59']"
        />
      </el-form-item>
      <el-form-item label="黑名单来源">
        <el-select
          v-model="queryParams.channel"
          placeholder="请选择来源"
          clearable
        >
          <el-option
            v-for="(item, index) in sourceList"
            :key="index"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="截止时间">
        <el-date-picker
          v-model="dataEnd"
          size="small"
          type="daterange"
          clearable
          style="width: 380px"
          value-format="yyyy-MM-dd"
          range-separator="-"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
        />
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
        >
          搜索
        </el-button>
        <el-button
          size="mini"
          @click="handleReset"
        >
          重置
        </el-button>
      </el-form-item>
    </el-form>
    <el-row style="margin-bottom: 20px;">
      <el-button
        type="primary"
        icon="el-icon-plus"
        size="mini"
        @click="handleAdd"
      >
        新增黑名单
      </el-button>
      <el-button
        type="primary"
        size="mini"
        @click="handleSource"
      >
        渠道黑名单列表
      </el-button>
    </el-row>
    <!-- 黑名单列表 -->
    <el-table
      v-loading="loading"
      border="border"
      :data="list"
    >
      <el-table-column
        label="编号"
        prop="id"
        align="center"
      />
      <el-table-column
        label="手机号"
        prop="mobile"
        align="center"
      />
      <el-table-column
        label="黑名单来源"
        prop="channel"
        align="center"
      />
      <el-table-column
        label="备注"
        prop="reason"
        align="center"
      />
      <el-table-column
        label="失效日期"
        prop="deadlineTime"
        align="center"
      />
      <el-table-column
        label="创建时间"
        prop="createdTime"
        align="center"
      />
      <el-table-column label="操作">
        <template slot-scope="scope">
          <el-button
            type="text"
            style="color: #c00"
            @click="hanleDel(scope.row)"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
    <!-- 新增 -->
    <el-dialog
      title="创建黑名单"
      :visible.sync="visible"
      width="660px"
      :before-close="close"
    >
      <el-form
        ref="ruleForm"
        :model="ruleForm"
        :rules="rules"
        size="small"
        label-width="130px"
      >
        <el-form-item
          label="手机号"
          prop="mobileList"
        >
          <el-input
            v-model="ruleForm.mobileList"
            :rows="4"
            placeholder="多个用英文逗号隔开"
            type="textarea"
            style="width: 380px;"
          />
        </el-form-item>
        <el-form-item
          label="截止时间"
          prop="deadlineEndTime"
        >
          <el-date-picker
            v-model="ruleForm.deadlineEndTime"
            type="date"
            placeholder="请选择截止时间"
            clearable
            style="width: 380px"
            value-format="yyyy-MM-dd HH:mm:ss"
          />
        </el-form-item>
        <el-form-item label="备注">
          <el-input
            v-model="ruleForm.reason"
            placeholder="请输入"
            clearable
            style="width: 380px;"
          />
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            @click="submitForm('ruleForm')"
          >
            保存
          </el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
    <!-- 渠道黑名单列表 -->
    <el-dialog
      title="渠道黑名单列表"
      :visible.sync="visibleSurce"
      width="880px"
    >
      <el-button
        type="primary"
        style="margin-bottom: 20px;"
        icon="el-icon-plus"
        @click="addChannel"
      >
        新增
      </el-button>
      <el-table
        v-loading="channelLoading"
        border="border"
        :data="channelList"
        max-height="600px"
      >
        <el-table-column
          label="渠道名称"
          prop="flowChannel"
          align="center"
        />
        <el-table-column
          label="黑名单失效日期"
          prop="deadlineTime"
          align="center"
          width="140px"
        />
        <el-table-column
          label="创建时间"
          prop="createdTime"
          align="center"
          width="140px"
        />
        <el-table-column
          label="创建人"
          prop="createdBy"
          align="center"
        />
        <el-table-column
          label="操作"
          width="80px"
        >
          <template slot-scope="scope">
            <el-button
              type="text"
              style="color: #c00"
              size="mini"
              plain
              @click="channelDel(scope.row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>
    <!-- 新增渠道 -->
    <el-dialog
      title="新增渠道"
      :visible.sync="visibleChannel"
      width="660px"
      :before-close="closeChannel"
    >
      <el-form
        ref="channelForm"
        :model="channelForm"
        :rules="channelRules"
        size="small"
        label-width="130px"
      >
        <el-form-item
          label="渠道名称"
          prop="flowChanel"
        >
          <el-select
            v-model="channelForm.flowChanel"
            placeholder="请选择渠道"
            clearable
            style="width: 380px"
          >
            <el-option
              v-for="(item, index) in channelArr"
              :key="index"
              :label="item"
              :value="item"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          label="黑名单失效时间"
          prop="deadlineTime"
        >
          <el-date-picker
            v-model="channelForm.deadlineTime"
            :picker-options="disabledDead"
            type="date"
            clearable
            placeholder="请选择失效时间"
            style="width: 380px"
            value-format="yyyy-MM-dd"
          />
        </el-form-item>
        <el-form-item>
          <el-button @click="closeChannel">
            取消
          </el-button>
          <el-button
            type="primary"
            @click="submitChannel('channelForm')"
          >
            保存
          </el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import {
  queryBlackList,
  deleteBlackList,
  saveBlackList,
  saveChannelBlack,
  queryChannelList, // 渠道黑名单列表
  queryChannels, // 获取下拉框渠道名称
  delFlowBlackList
} from '@/api/marketing'
import { parseTime } from '@/utils/index'

export default {
  name: 'BlackList',
  data() {
    return {
      loading: false,
      channelLoading: false,
      total: 0,
      list: [],
      channelArr: [], // 渠道列表
      channelList: [], // 渠道列表
      // 查询参数
      queryParams: {
        mobile: undefined,
        channel: undefined, // 黑名单来源
        pageNum: 1,
        pageSize: 10
      },
      dataVal: [], // 添加时间
      dataEnd: [], // 截止时间
      sourceList: [
        { label: '短信渠道', value: 'SMS' },
        { label: '外呼渠道', value: 'CALL_OUT' },
        { label: '人工录入', value: 'MANUAL_ENTRY' }
      ], // 黑名单来源列表
      pickerOptionsDefault: {
        onPick: ({ maxDate, minDate }) => {
          //当我们选择两个值的时候，就认为用户已经选择完毕
          if (maxDate != null && minDate != null) {
            this.repayDateBegin = maxDate;
            this.repayDateEnd = minDate;
          }
        },
        disabledDate: (time) => {
          let maxDate = this.maxDate;
          let minDate = this.minDate;
          if (maxDate != null && minDate != null) {
            let days = maxDate.getTime() - minDate.getTime();
            //计算完之后必须清除，否则选择器一直处于禁止选择的状态
            this.maxDate = null;
            this.minDate = null;
            return parseInt(days / (1000 * 60 * 60 * 24)) > 30;
          } else {
            //设置当前时间后的时间不可选
            return time.getTime() > Date.now();
          }
        },
      },
      disabledDead: {
        disabledDate: (time) => {
          return time.getTime() < Date.now()
        }
      }, // 截止时间
      maxDate: null,
      minDate: null,
      channelForm: {
        flowChanel: '',
        deadlineTime: ''
      }, // 新增渠道表单
      visible: false,
      visibleSurce: false, // 渠道黑名单列表
      visibleChannel: false, // 新增渠道
      ruleForm: {
        mobileList: undefined,
        deadlineEndTime: undefined,
        reason: ''
      },
      rules: {
        mobileList: [
          { required: true, message: '手机号不能为空', trigger: 'blur' },
          {
            validator: (rule, value, callback) => {
              const regExp = /^[0-9,]*$/
              if (value && !regExp.test(value)) {
                callback(new Error('只能包含数字和英文逗号'))
              } else {
                callback()
              }
            },
            trigger: 'blur'
          }
        ],
        deadlineEndTime: [
          { required: true, message: '截止日期不能为空', trigger: 'change' }
        ]
      },
      channelRules: {
        flowChanel: [
          { required: true, message: '渠道名称不能为空', trigger: 'blur' }
        ]
      }
    }
  },
  watch: {
    dataVal(val) {
      if (val && val.length === 2) {
        this.queryParams.beginTime = val[0]
        this.queryParams.endTime = val[1]
      } else {
        this.queryParams.beginTime = undefined
        this.queryParams.endTime = undefined
      }
    },
    dataEnd(val) {
      if (val && val.length === 2) {
        this.queryParams.deadlineBeginTime = val[0]
        this.queryParams.deadlineEndTime = val[1]
      } else {
        this.queryParams.deadlineBeginTime = undefined
        this.queryParams.deadlineEndTime = undefined
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    /** 查询参数列表 */
    getList() {
      this.loading = true

      let params = {}

      params = {
        ...params,
        ...this.queryParams
      }
      queryBlackList(params).then(res => {
        this.loading = false
        if (res.data) {
          this.list = res.data.list || []
          this.list = this.list.map(item => {
            item.createdTime = parseTime(item.createdTime)
            return item
          })
          this.total = res.data.total
          this.loading = false
        }
      })
    },
    // 查询按钮
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },

    // 重置表单
    handleReset() {
      this.$refs['queryForm'].resetFields()
      this.queryParams = {
        mobile: undefined,
        pageNum: 1,
        pageSize: 10
      }
      this.dataVal = []
      this.dataEnd = []
      // 刷新列表
      this.getList()
    },

    // 新增弹窗
    handleAdd() {
      this.visible = true
    },
    // add渠道弹窗
    addChannel() {
      this.visibleChannel = true
      queryChannels().then((res) => {
        this.channelArr = res.data
        this.visibleChannel = true
      })
    },
    // 来源弹窗
    handleSource() {
      this.visibleSurce = true
      this.getChannelList()
    },
    // 关闭弹窗
    closeSurce() {
      this.visibleSurce = false
    },
    // 重置表单
    close() {
      this.visible = false
      this.$refs['ruleForm'].resetFields()
    },
    // 重置新增渠道表单
    closeChannel() {
      this.visibleChannel = false
      this.$refs['channelForm'].resetFields()
    },
    // 创建提交
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          const params = {
            ...this.ruleForm
          }
          saveBlackList(params).then(() => {
            this.$message.success('创建成功')
            this.close()
            this.getList()
          })
        }
      })
    },
    // 新增渠道提交
    submitChannel(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          const params = {
            ...this.channelForm
          }
          saveChannelBlack(params).then(() => {
            this.$message.success('新增成功')
            this.closeChannel()
            this.getChannelList()
          })
        }
      })
    },

    // 删除
    hanleDel(row) {
      this.$confirm(`是否确认删除?`, "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        roundButton: true,
        type: "warning",
      }).then(() => {
        let params = [row.id];
        deleteBlackList(params).then(() => {
          this.$message.success("删除成功");
          this.getList();
        });
      });
    },
    // 打开渠道黑名单列表弹窗
    openChannelList() {
      this.visibleSurce = true
      this.getChannelList()
    },
    // 获取渠道黑名单列表
    getChannelList() {
      this.channelLoading = true
      queryChannelList().then((res) => {
        this.channelLoading = false
        this.channelList = res.data
      })
    },
    // 渠道黑名单列表删除
    channelDel(row) {
      this.$confirm(`是否将此渠道移出黑名单?`, '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        roundButton: true,
        type: 'warning'
      }).then(() => {
        const params = { flowChanel: row.flowChannel }
        delFlowBlackList(params).then(() => {
          this.$message.success('删除成功')
          this.getChannelList()
        })
      })
    }
  }
}
</script>
