import request from '@/utils/request'

// 短信记录查询
export function queryRecord(data) {
  return request({
    url: 'sms/queryRecord',
    method: 'post',
    data
  })
}

// 短信模板查询
export function queryTemplate(data) {
  return request({
    url: 'sms/queryTemplate',
    method: 'post',
    data
  })
}

// 短信发送
export function sendMessage(data) {
  return request({
    url: 'sms/sendMessage',
    method: 'post',
    data
  })
}

