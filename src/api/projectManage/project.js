import request from '@/utils/request'

// 主列表查询
export function getList(data) {
  return request({
    url: 'projectInfo/page',
    method: 'get',
    params: data
  })
}

// 资产方列表查询
export function getAssetList(data) {
  return request({
    url: 'api/flowConfig/queryPage',
    method: 'get',
    params: data
  })
}

// 融担方列表查询
export function getGuaranteeList(data) {
  return request({
    url: 'api/guaranteeConfig/queryGuaranteeConfig',
    method: 'get',
    params: data
  })
}

// 资金方列表查询
export function getFundList(data) {
  return request({
    url: 'api/capitalConfig/queryCapitalConfig',
    method: 'get',
    params: data
  })
}

// 新增项目
export function addProject(data) {
  return request({
    url: 'projectInfo/create',
    method: 'post',
    data
  })
}

// 启用/停用
export function updateStatus(data) {
  return request({
    url: 'projectInfo/enable',
    method: 'post',
    data
  })
}

// 详情查询
export function getDetail(data) {
  return request({
    url: 'projectInfo/info',
    method: 'get',
    params: data
  })
}

// 项目修改
export function updateProject(data) {
  return request({
    url: 'projectInfo/update',
    method: 'post',
    data
  })
}

// 对客协议列表查询
export function getAgreementList(data) {
  return request({
    url: 'projectAgreement/page',
    method: 'get',
    params: data
  })
}

// 临时配置项列表查询
export function getConfigList(data) {
  return request({
    url: 'projectInfo/temPage',
    method: 'get',
    params: data
  })
}

// 临时项目修改
export function updateTemProject(data) {
  return request({
    url: 'api/projectElements/edit',
    method: 'post',
    data
  })
}

// 临时配置项详情查询
export function getConfigDtl(data) {
  return request({
    url: 'api/projectElements/infoById',
    method: 'get',
    params: data
  })
}

// 协议新增
export function addAgreement(data) {
  return request({
    url: 'projectAgreement/create',
    method: 'post',
    data
  })
}

// 协议修改
export function updateAgreement(data) {
  return request({
    url: 'projectAgreement/update',
    method: 'post',
    data
  })
}

// 对客协议列表查询
export function getAgreementDtl(data) {
  return request({
    url: 'projectAgreement/info',
    method: 'get',
    params: data
  })
}

// 协议上传
export function upload(data) {
  return request({
    url: 'api/file/upload',
    method: 'post',
    data
  })
}

// 删除协议
export function delAgreement(data) {
  return request({
    url: 'projectAgreement/del',
    method: 'post',
    data
  })
}

// 资金方查询
export function getFundOwer(data) {
  return request({
    url: 'api/capitalConfig/queryByChannel',
    method: 'get',
    params: data
  })
}

// 融担方查询
export function getGuaOwer(data) {
  return request({
    url: 'api/guaranteeConfig/queryByCode',
    method: 'get',
    params: data
  })
}

// 新增临时配置项
export function addConfig(data) {
  return request({
    url: 'api/projectElements/add',
    method: 'post',
    data
  })
}
