import request from '@/utils/request'

// 查询
export function query(data) {
  return request({
    url: '/app/push/query',
    method: 'post',
    data
  })
}

// 新建版本
export function add(data) {
  return request({
    url: '/app/push/add',
    method: 'post',
    data
  })
}

// 客户端编辑
export function update(data) {
  return request({
    url: '/app/push/update',
    method: 'post',
    data
  })
}

// 渠道查询
export function channelsQuery(data) {
  return request({
    url: '/app/channel/query',
    method: 'post',
    data
  })
}

// 渠道新增
export function channelsAdd(data) {
  return request({
    url: '/app/channel/add',
    method: 'post',
    data
  })
}

// 渠道更新
export function channelsUpdate(data) {
  return request({
    url: '/app/channel/update',
    method: 'post',
    data
  })
}


















