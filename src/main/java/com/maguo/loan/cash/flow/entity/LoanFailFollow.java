package com.maguo.loan.cash.flow.entity;


import com.jinghang.capital.api.dto.BankChannel;
import com.maguo.loan.cash.flow.enums.FlowChannel;
import com.maguo.loan.cash.flow.enums.WhetherState;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Table;

import java.time.LocalDateTime;

@Entity
@Table(name = "loan_fail_follow")
public class LoanFailFollow extends BaseEntity {

    /**
     * 借据id
     */
    private String loanId;

    /**
     * 资金id
     */
    private String loanNo;

    /**
     * 订单id
     */
    private String orderId;

    /**
     * 流量方订单编号
     */
    private String outerOrderId;
    /**
     * 手机号
     */
    private String mobile;
    /**
     * 身份证号
     */
    private String certNo;
    /**
     * 姓名
     */
    private String name;
    /**
     * 流量方
     */
    @Enumerated(EnumType.STRING)
    private FlowChannel flowChannel;
    /**
     * 资金方
     */
    @Enumerated(EnumType.STRING)
    private BankChannel bankChannel;
    /**
     *是否处理
     */
    @Enumerated(EnumType.STRING)
    private WhetherState isProcess;
    /**
     * 申请时间
     */
    private LocalDateTime applyTime;
    /**
     * 失败原因
     */
    private String failReason;

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public String getOuterOrderId() {
        return outerOrderId;
    }

    public void setOuterOrderId(String outerOrderId) {
        this.outerOrderId = outerOrderId;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getCertNo() {
        return certNo;
    }

    public void setCertNo(String certNo) {
        this.certNo = certNo;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public FlowChannel getFlowChannel() {
        return flowChannel;
    }

    public void setFlowChannel(FlowChannel flowChannel) {
        this.flowChannel = flowChannel;
    }

    public BankChannel getBankChannel() {
        return bankChannel;
    }

    public void setBankChannel(BankChannel bankChannel) {
        this.bankChannel = bankChannel;
    }

    public String getFailReason() {
        return failReason;
    }

    public void setFailReason(String failReason) {
        this.failReason = failReason;
    }

    public WhetherState getIsProcess() {
        return isProcess;
    }

    public void setIsProcess(WhetherState isProcess) {
        this.isProcess = isProcess;
    }
    public LocalDateTime getApplyTime() {
        return applyTime;
    }
    public void setApplyTime(LocalDateTime applyTime) {
        this.applyTime = applyTime;
    }

    public String getLoanId() {
        return loanId;
    }

    public void setLoanId(String loanId) {
        this.loanId = loanId;
    }

    public String getLoanNo() {
        return loanNo;
    }

    public void setLoanNo(String loanNo) {
        this.loanNo = loanNo;
    }
}
