package com.maguo.loan.cash.flow.util;

import com.maguo.loan.cash.flow.enums.Education;
import com.maguo.loan.cash.flow.enums.Gender;
import com.maguo.loan.cash.flow.enums.Marriage;

/**
 * <AUTHOR>
 */
public class PushMkConvertUtil {
    public static String educationToBackgroundCode(Education education) {
        if (education == null) return null;

        return switch (education) {
            case PRIMARY_SCHOOL -> "1";
            case JUNIOR_HIGH_SCHOOL -> "2";
            case HIGH_SCHOOL -> "3";
            case JUNIOR_COLLEGE -> "4";
            case COLLEGE -> "5";
            case MASTER, DOCTOR -> "6";
            default -> null;
        };
    }


    public static Integer marriageTo(Marriage marriage) {
        if (marriage == null) return 3;

        return switch (marriage) {
            case UNMARRIED -> 2;
            case MARRIED -> 1;
            case DIVORCED, WIDOWED, UNKNOWN -> 3;
        };
    }


    public static String genderToSex(Gender gender) {
        if (gender == null) return null;

        return switch (gender) {
            case MALE -> "1";
            case FEMALE -> "2";
            case UNKNOWN -> "3";
            default -> null;
        };
    }
}
