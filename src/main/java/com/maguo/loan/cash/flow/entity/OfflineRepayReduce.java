package com.maguo.loan.cash.flow.entity;


import com.jinghang.common.util.StringUtil;
import com.maguo.loan.cash.flow.enums.AuditStatus;
import com.maguo.loan.cash.flow.enums.RepayPurpose;
import com.maguo.loan.cash.flow.enums.UseState;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Table;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 线下还款减免表
 *
 * <AUTHOR>
 */
@Entity
@Table(name = "offline_repay_reduce")
public class OfflineRepayReduce extends BaseEntity {

    /**
     * 订单号
     */
    private String orderId;
    /**
     * 借据号
     */
    private String loanId;
    /**
     * 期数
     */
    private Integer period;
    /**
     * 还款模式
     */
    @Enumerated(EnumType.STRING)
    private RepayPurpose repayPurpose;

    /**
     * 应还本金
     */
    private BigDecimal principalAmt;
    /**
     * 应还利息
     */
    private BigDecimal interestAmt;
    /**
     * 应还担保费
     */
    private BigDecimal guaranteeAmt;
    /**
     * 应还罚息
     */
    private BigDecimal penaltyAmt;
    /**
     * 应还咨询费
     */
    private BigDecimal consultFee;
    /**
     * 应还总金额
     */
    private BigDecimal amount;
    /**
     * 减免金额
     */
    private BigDecimal reduceAmount;

    /**
     * 实还总金额
     */
    private BigDecimal actAmount;

    /**
     * 审核状态
     */
    @Enumerated(EnumType.STRING)
    private AuditStatus auditState;

    /**
     * 使用状态
     */
    @Enumerated(EnumType.STRING)
    private UseState useState;

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public String getLoanId() {
        return loanId;
    }

    public void setLoanId(String loanId) {
        this.loanId = loanId;
    }

    public Integer getPeriod() {
        return period;
    }

    public void setPeriod(Integer period) {
        this.period = period;
    }

    public RepayPurpose getRepayPurpose() {
        return repayPurpose;
    }

    public void setRepayPurpose(RepayPurpose repayPurpose) {
        this.repayPurpose = repayPurpose;
    }

    public BigDecimal getPrincipalAmt() {
        return principalAmt;
    }

    public void setPrincipalAmt(BigDecimal principalAmt) {
        this.principalAmt = principalAmt;
    }

    public BigDecimal getInterestAmt() {
        return interestAmt;
    }

    public void setInterestAmt(BigDecimal interestAmt) {
        this.interestAmt = interestAmt;
    }

    public BigDecimal getGuaranteeAmt() {
        return guaranteeAmt;
    }

    public void setGuaranteeAmt(BigDecimal guaranteeAmt) {
        this.guaranteeAmt = guaranteeAmt;
    }

    public BigDecimal getPenaltyAmt() {
        return penaltyAmt;
    }

    public void setPenaltyAmt(BigDecimal penaltyAmt) {
        this.penaltyAmt = penaltyAmt;
    }

    public BigDecimal getConsultFee() {
        return consultFee;
    }

    public void setConsultFee(BigDecimal consultFee) {
        this.consultFee = consultFee;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public BigDecimal getReduceAmount() {
        return reduceAmount;
    }

    public void setReduceAmount(BigDecimal reduceAmount) {
        this.reduceAmount = reduceAmount;
    }

    public BigDecimal getActAmount() {
        return actAmount;
    }

    public void setActAmount(BigDecimal actAmount) {
        this.actAmount = actAmount;
    }

    public AuditStatus getAuditState() {
        return auditState;
    }

    public void setAuditState(AuditStatus auditState) {
        this.auditState = auditState;
    }

    public UseState getUseState() {
        return useState;
    }

    public void setUseState(UseState useState) {
        this.useState = useState;
    }

    @Override
    protected String prefix() {
        return "ORR";
    }


    @Override
    public void prePersist() {
        if (StringUtil.isBlank(super.getId())) {
            setId(genId());
        }
        if (StringUtil.isBlank(super.getCreatedBy())) {
            super.setCreatedBy("sys");
        }
        if (super.getCreatedTime() == null) {
            super.setCreatedTime(LocalDateTime.now());
        }
        super.setUpdatedBy("sys");
        super.setUpdatedTime(LocalDateTime.now());
    }
}
