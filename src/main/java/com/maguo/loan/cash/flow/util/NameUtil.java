package com.maguo.loan.cash.flow.util;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @Description
 * @Date 2024/12/10 16:51
 **/
public class NameUtil {

    /**
     * 用户姓名脱敏
     * @param name
     * @return
     */
    public static String maskName(String name) {
        if (StringUtils.isBlank(name)) {
            return name;
        }
        if (name.length() == NumConstants.TWO) {
            name = name.charAt(0) + "*";
        } else if (name.length() > NumConstants.TWO) {
            name = name.charAt(0) + "*".repeat(name.length() - NumConstants.TWO) + name.substring(name.length() - NumConstants.ONE);
        }
        return name;
    }
}
