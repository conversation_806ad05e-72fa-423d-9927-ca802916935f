package com.maguo.loan.cash.flow.entrance.common.enums;

/**
 * 还款结果推送，还款状态
 *
 * <AUTHOR>
 * @date 2024/6/14
 */
public enum RepayResult {

    REPAY_PROCESSING("还款中"),
    REPAY_PART_SUCCESS("部分还款成功"),
    REPAY_SUCCESS("还款成功"),
    REPAY_FAIL("还款失败");

    private String description;

    RepayResult(String description) {
        this.description = description;
    }

    public String getDescription() {
        return description;
    }
}
