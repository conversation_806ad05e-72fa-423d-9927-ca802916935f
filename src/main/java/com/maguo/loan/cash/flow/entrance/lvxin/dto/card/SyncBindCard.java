package com.maguo.loan.cash.flow.entrance.lvxin.dto.card;

/**
* <AUTHOR>
* @Description 银行卡签约信息  目前还款接口使用
* @Date 2025-05-17
* @Version 1.0
*/
public class SyncBindCard {
    /**
     * 支付渠道（如：ALIPAY、WECHAT）
     */
    private String payChannel;

    /**
     * 绑卡类型（如：DEBIT_CARD、CREDIT_CARD）
     */
    private String bindType;

    /**
     * 共享协议码
     */
    private String cntNo;

    /**
     * 手机号
     */
    private String phoneNo;

    /**
     * 证件类型（如：ID_CARD）
     */
    private String certType;

    /**
     * 证件号码
     */
    private String idNo;

    /**
     * 银行卡号
     */
    private String acctNo;

    /**
     * 持卡人姓名
     */
    private String acctName;

    public String getPayChannel() {
        return payChannel;
    }

    public void setPayChannel(String payChannel) {
        this.payChannel = payChannel;
    }

    public String getBindType() {
        return bindType;
    }

    public void setBindType(String bindType) {
        this.bindType = bindType;
    }

    public String getCntNo() {
        return cntNo;
    }

    public void setCntNo(String cntNo) {
        this.cntNo = cntNo;
    }

    public String getPhoneNo() {
        return phoneNo;
    }

    public void setPhoneNo(String phoneNo) {
        this.phoneNo = phoneNo;
    }

    public String getCertType() {
        return certType;
    }

    public void setCertType(String certType) {
        this.certType = certType;
    }

    public String getIdNo() {
        return idNo;
    }

    public void setIdNo(String idNo) {
        this.idNo = idNo;
    }

    public String getAcctNo() {
        return acctNo;
    }

    public void setAcctNo(String acctNo) {
        this.acctNo = acctNo;
    }

    public String getAcctName() {
        return acctName;
    }

    public void setAcctName(String acctName) {
        this.acctName = acctName;
    }
}
