package com.maguo.loan.cash.flow.dto.repush;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.maguo.loan.cash.flow.enums.RightsSupplier;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 */
public class RightsOrderApplyDto {

    private List<String> rightsOrderSourceIds;

    private RightsSupplier rightsSupplier;

    private String remark;

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime beginTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;

    public List<String> getRightsOrderSourceIds() {
        return rightsOrderSourceIds;
    }

    public void setRightsOrderSourceIds(List<String> rightsOrderSourceIds) {
        this.rightsOrderSourceIds = rightsOrderSourceIds;
    }

    public RightsSupplier getRightsSupplier() {
        return rightsSupplier;
    }

    public void setRightsSupplier(RightsSupplier rightsSupplier) {
        this.rightsSupplier = rightsSupplier;
    }

    public LocalDateTime getBeginTime() {
        return beginTime;
    }

    public void setBeginTime(LocalDateTime beginTime) {
        this.beginTime = beginTime;
    }

    public LocalDateTime getEndTime() {
        return endTime;
    }

    public void setEndTime(LocalDateTime endTime) {
        this.endTime = endTime;
    }
}
