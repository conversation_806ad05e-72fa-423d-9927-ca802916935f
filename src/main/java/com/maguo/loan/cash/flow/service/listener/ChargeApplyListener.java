package com.maguo.loan.cash.flow.service.listener;



import com.maguo.loan.cash.flow.config.RabbitConfig;
import com.maguo.loan.cash.flow.service.AbstractListener;
import com.maguo.loan.cash.flow.service.MqService;
import com.maguo.loan.cash.flow.service.WarningService;
import com.maguo.loan.cash.flow.service.pay.ThirdPayService;
import com.rabbitmq.client.Channel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;

/**
 * <AUTHOR>
 * @date 2023/5/31
 * 扣款结果
 */
@Component
public class ChargeApplyListener extends AbstractListener {
    private static final Logger logger = LoggerFactory.getLogger(ChargeApplyListener.class);

    public ChargeApplyListener(MqService mqService, WarningService mqWarningService) {
        super(mqService, mqWarningService);
    }

    @Autowired
    private ThirdPayService thirdPayService;

    @Autowired
    private WarningService warningService;

    @RabbitListener(queues = RabbitConfig.Queues.CHARGE_APPLY)
    public void listenChargeApply(Message message, Channel channel) {
        String chargeId = new String(message.getBody(), StandardCharsets.UTF_8);
        try {
            logger.info("监听扣款申请:{}", chargeId);
            thirdPayService.apply(chargeId);
        } catch (Exception e) {
            warningService.warn("扣款申请异常:" + chargeId, msg -> logger.error("扣款申请异常:{},", chargeId, e));
        } finally {
            ackMsg(chargeId, message, channel);
        }
    }

}
