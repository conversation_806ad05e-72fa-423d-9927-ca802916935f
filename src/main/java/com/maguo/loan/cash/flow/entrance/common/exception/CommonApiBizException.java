package com.maguo.loan.cash.flow.entrance.common.exception;

/**
 * 火山融
 * <AUTHOR>
 */
public class CommonApiBizException extends RuntimeException {

    private final CommonApiResultCode resultCode;

    public CommonApiBizException(CommonApiResultCode resultCode) {
        super(resultCode.getMsg());
        this.resultCode = resultCode;
    }

    public CommonApiBizException(String message, CommonApiResultCode resultCode) {
        super(message);
        this.resultCode = resultCode;
    }

    public CommonApiResultCode getResultCode() {
        return resultCode;
    }

}
