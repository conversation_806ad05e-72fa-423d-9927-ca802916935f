package com.maguo.loan.cash.flow.dto;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @since 2025-02-21
 */
public class UserRevolvingRangeDTO {

    private BigDecimal min;

    private BigDecimal max;

    private BigDecimal binningAmount;

    private BigDecimal totalAmount;

    private BigDecimal availableAmount;

    public BigDecimal getMin() {
        return min;
    }

    public void setMin(BigDecimal min) {
        this.min = min;
    }

    public BigDecimal getMax() {
        return max;
    }

    public void setMax(BigDecimal max) {
        this.max = max;
    }

    public BigDecimal getBinningAmount() {
        return binningAmount;
    }

    public void setBinningAmount(BigDecimal binningAmount) {
        this.binningAmount = binningAmount;
    }

    public BigDecimal getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(BigDecimal totalAmount) {
        this.totalAmount = totalAmount;
    }

    public BigDecimal getAvailableAmount() {
        return availableAmount;
    }

    public void setAvailableAmount(BigDecimal availableAmount) {
        this.availableAmount = availableAmount;
    }
}
