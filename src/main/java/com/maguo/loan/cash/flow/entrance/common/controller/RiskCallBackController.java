package com.maguo.loan.cash.flow.entrance.common.controller;

import com.maguo.loan.cash.flow.remote.riskdata.req.RiskCallbackRequest;
import com.maguo.loan.cash.flow.service.RiskService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/jh/risk")
public class RiskCallBackController {

    @Autowired
    private RiskService riskService;

    /**
     * 风控回调
     */
    @PostMapping("/callback")
    public String callback(@RequestBody RiskCallbackRequest request) {
        return riskService.riskCallback(request);
    }
}
