package com.maguo.loan.cash.flow.entrance.ppd.dto;

import com.fasterxml.jackson.annotation.JsonInclude;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @Description
 * @Date 2024/12/27 16:42
 **/
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PpdCommonResponse implements Serializable {
    @Serial
    private static final long serialVersionUID = -3296521925086928295L;

    /**
     * 请求结果状态
     */
    private String status;

    /**
     * 合作方的在信也的渠道号
     */
    private String channelA;
    /**
     * 信也在合作方的渠道号
     */
    private String channelB;
    /**
     * 接口ID
     */
    private String serviceId;
    /**
     * 请求流水号
     */
    private String seqNo;
    /**
     * 响应日期  格式YYYYMMDD，如20170321
     */
    private String transDate;
    /**
     * 响应时间  HHmmssSSS 如152031123
     */
    private String transTime;
    /**
     * 业务数据
     */
    private String data;
    /**
     * 业务数据签名 加密串
     */
    private String sign;
    /**
     * 请求响应码
     * 成功返回“0000”
     */
    private String returnCode;
    /**
     * 请求响应描述
     * 成功则“请求成功”，失败则具体错误信息
     */
    private String returnMsg;

    public PpdCommonResponse bizFail() {
        PpdCommonResponse response = new PpdCommonResponse();


        return response;
    }

    public PpdCommonResponse sysFail() {
        PpdCommonResponse response = new PpdCommonResponse();


        return response;
    }

    public PpdCommonResponse success() {
        PpdCommonResponse response = new PpdCommonResponse();


        return response;
    }

    public String getChannelA() {
        return channelA;
    }

    public void setChannelA(String channelA) {
        this.channelA = channelA;
    }

    public String getChannelB() {
        return channelB;
    }

    public void setChannelB(String channelB) {
        this.channelB = channelB;
    }

    public String getServiceId() {
        return serviceId;
    }

    public void setServiceId(String serviceId) {
        this.serviceId = serviceId;
    }

    public String getSeqNo() {
        return seqNo;
    }

    public void setSeqNo(String seqNo) {
        this.seqNo = seqNo;
    }

    public String getTransDate() {
        return transDate;
    }

    public void setTransDate(String transDate) {
        this.transDate = transDate;
    }

    public String getTransTime() {
        return transTime;
    }

    public void setTransTime(String transTime) {
        this.transTime = transTime;
    }

    public String getData() {
        return data;
    }

    public void setData(String data) {
        this.data = data;
    }

    public String getSign() {
        return sign;
    }

    public void setSign(String sign) {
        this.sign = sign;
    }

    public String getReturnCode() {
        return returnCode;
    }

    public void setReturnCode(String returnCode) {
        this.returnCode = returnCode;
    }

    public String getReturnMsg() {
        return returnMsg;
    }

    public void setReturnMsg(String returnMsg) {
        this.returnMsg = returnMsg;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }
}
