package com.maguo.loan.cash.flow.service.agreement;

public enum LXProtocolType {

    COMPREHENSIVE_AUTHORIZATION_LETTER_GUARANTEE(1, "authorization01.pdf","授信（个人敏感信息授权书-修订）"),
    PERSONAL_INFORMATION_AUTHORIZATION_LETTER(2, "authorization02.pdf","授信（综合授权书-担保）"),
    AUTHORIZATION_LETTER_FOR_ENTRUSTED_DEDUCTION_GUARANTEE(3, "authorization03.pdf","委托扣款授权书-担保vs借款人"),
    PERSONAL_LOAN_CONTRACT(4, "authorization04.pdf","借款合同"),
    CONSULTING_SERVICE_CONTRACT(5, "authorization05.pdf","咨询服务合同(超捷)"),
    LETTER_OF_COMMITMENT(6, "authorization06.pdf","承诺书")

    ;
    static String toProtocolType(String fileType) {
      return   switch (fileType) {
            case "COMPREHENSIVE_AUTHORIZATION_LETTER_GUARANTEE"  -> "authorization01.pdf";
            case "PERSONAL_INFORMATION_AUTHORIZATION_LETTER" -> "authorization02.pdf";
            case "AUTHORIZATION_LETTER_FOR_ENTRUSTED_DEDUCTION_GUARANTEE" -> "authorization03.pdf";
            case "PERSONAL_LOAN_CONTRACT" -> "authorization04.pdf";
            case "CONSULTING_SERVICE_CONTRACT" -> "authorization05.pdf";
            case "LETTER_OF_COMMITMENT" -> "authorization06.pdf";
            default ->"";
        };
    }

    private Integer code;

    private String agreement;

    private String description;

    LXProtocolType(Integer code, String agreement,String description) {
        this.code = code;
        this.agreement=agreement;
        this.description = description;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getAgreement() {
        return agreement;
    }

    public void setAgreement(String agreement) {
        this.agreement = agreement;
    }
}
