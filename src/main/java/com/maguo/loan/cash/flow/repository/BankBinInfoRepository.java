package com.maguo.loan.cash.flow.repository;

import com.maguo.loan.cash.flow.entity.BankBinInfo;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;


public interface BankBinInfoRepository extends JpaRepository<BankBinInfo, Integer> {

    @Query(value = "FROM BankBinInfo bi WHERE bi.cardBin = left(?1, bi.cardBinLength) order by bi.cardBinLength limit 1")
    BankBinInfo queryBankBinInfoByCardNo(String cardNo);

}
