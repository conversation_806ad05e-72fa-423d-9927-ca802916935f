package com.maguo.loan.cash.flow.service.listener;


import com.maguo.loan.cash.flow.config.RabbitConfig;
import com.maguo.loan.cash.flow.service.AbstractListener;
import com.maguo.loan.cash.flow.service.MqService;
import com.maguo.loan.cash.flow.service.RepayPlanService;
import com.maguo.loan.cash.flow.service.WarningService;
import com.rabbitmq.client.Channel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;

/**
 * 还款计划同步 fin-core
 */
@Component
public class RepayPlanSyncListener extends AbstractListener {
    private static final Logger logger = LoggerFactory.getLogger(RepayPlanSyncListener.class);

    @Autowired
    private WarningService warningService;
    @Autowired
    private RepayPlanService repayPlanService;

    public RepayPlanSyncListener(MqService mqService, WarningService mqWarningService) {
        super(mqService, mqWarningService);
    }

    @RabbitListener(queues = RabbitConfig.Queues.REPAY_PLAN_SYNC)
    public void listenRepayApply(Message message, Channel channel) {
        String loanId = new String(message.getBody(), StandardCharsets.UTF_8);
        try {
            logger.info("还款计划同步 fin-core:{}", loanId);
            repayPlanService.repayPlanSyncToCore(loanId);
            // service
        } catch (Exception e) {
            warningService.warn("还款计划同步到fin-core失败:" + loanId + "," + e.getMessage(), msg -> logger.error("还款计划同步 fin-core:{},", loanId, e));
        } finally {
            ackMsg(loanId, message, channel);
        }
    }
}
