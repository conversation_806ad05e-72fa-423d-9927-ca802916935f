package com.maguo.loan.cash.flow.enums;

/**
 * <AUTHOR>
 * @date 2023/10/26
 */
public enum SmsTemplate {
    REGISTER_LOGIN("2", "QH001", "登录注册", "注册登录验证码", AbleStatus.ENABLE),
    PASSWORD_RESET("2", "QH002", "登录注册", "申请密码重置", AbleStatus.ENABLE),
    /**
     * 授信
     */
    AMOUNT_PASSED("2", "QH003", "授信", "获额通过", AbleStatus.ENABLE),
    CREDIT_PASS("2", "QH004", "授信", "授信通过", AbleStatus.ENABLE),
    CREDIT_REFUSE("2", "QH005", "授信", "授信拒绝", AbleStatus.ENABLE),
    /**
     * 放款
     */
    LOAN_CONFIRM("2", "QH006", "放款", "要款确认", AbleStatus.ENABLE),
    AMOUNT_NOT_APPLIED("2", "QH007", "放款", "获额未申请", AbleStatus.ENABLE), //MQ延迟
    MINI_WECHAT_QHYX_AMOUNT_NOT_APPLIED("2", "QH0071", "放款", "获额未申请", AbleStatus.ENABLE), //MQ延迟
    LOAN_APPLY_REMIND("2", "QH008", "放款", "未要款提醒", AbleStatus.ENABLE), //MQ延迟
    LOAN_SUCCESS("2", "QH009", "放款", "放款成功", AbleStatus.ENABLE),

    /**
     * 还款
     */
    REPAY_REMIND("2", "QH010", "还款", "还款提醒", AbleStatus.ENABLE),
    REPAY_DAY_DEDUCT_REMIND("2", "QH011", "还款", "还款日", AbleStatus.ENABLE),
    REPAY_BATCH_SUCCEED("2", "QH012", "还款", "还款成功", AbleStatus.ENABLE),
    REPAY_BATCH_FAILED("2", "QH013", "还款", "还款失败", AbleStatus.ENABLE),
    REPAY_CLEAR_SUCCEED("2", "QH014", "还款", "提前结清全部成功", AbleStatus.ENABLE),
    REPAY_CLEAR_FAILED("2", "QH015", "还款", "提前结清全部失败", AbleStatus.ENABLE),
    REPAY_NORMAL_COMPLETED("2", "QH016", "还款", "正常还款全部完成", AbleStatus.ENABLE),
    /**
     * 催收
     */
    OVERDUE_FIRST_DAY_REMIND("2", "QH017", "催收", "逾期一天", AbleStatus.ENABLE),
    OVERDUE_SECOND_DAY_REMIND("2", "QH018", "催收", "逾期两天", AbleStatus.ENABLE),
    /**
     * 客诉
     */
    LOSS_OF_COMMUNICATION_REMIND("2", "QH031", "客诉", "失联", AbleStatus.ENABLE),
    LOSS_OF_COMMUNICATION_END("2", "QH032", "客诉", "失联结案", AbleStatus.ENABLE),
    COLD_CASE_CLOSED("2", "QH033", "客诉", "冷处理结案", AbleStatus.ENABLE),
    /**
     * 权益
     */
    RIGHTS_CONFIRM("2", "QH024", "权益", "确认购买", AbleStatus.DISABLE),
    RIGHTS_DEDUCT_SUCCEED("2", "QH025", "权益", "权益扣款成功", AbleStatus.ENABLE),
    RIGHTS_DEDUCT_REMIND("2", "QH026", "权益", "付款提醒（放款后10日）", AbleStatus.DISABLE),
    RIGHTS_DEDUCT_FAILED("2", "QH027", "权益", "权益扣款失败", AbleStatus.DISABLE),
    RIGHTS_EQUITY_LINK("2", "QH028", "客服（手动）", "权益行权链接短信", AbleStatus.ENABLE),
    QING_HUA_APP_URL("2", "QH029", "客服（手动）", "优品app链接短信", AbleStatus.ENABLE),
    QING_HUA_APP_URL_V2("2", "QH-KF-241218001", "客服（手动）", "官网链接app下载", AbleStatus.ENABLE),

    QYHP_AGGREGATE_PAY_URL("2", "QH1188", "", "聚合订单支付链接(优品)", AbleStatus.ENABLE),
    XCYP_AGGREGATE_PAY_URL("2", "XC1188", "", "聚合订单支付链接(新橙优品)", AbleStatus.ENABLE),
    QING_HUA_APP_URL_WY("2", "QH030", "权益/客服（手动）", "权益扣款成功app短信链接(纬雅)", AbleStatus.ENABLE);
    private String smsType;
    private String templateNo;
    private String templateType;
    private String desc;
    private AbleStatus ableStatus;

    SmsTemplate(String smsType, String templateNo, String templateType, String desc, AbleStatus ableStatus) {
        this.smsType = smsType;
        this.templateNo = templateNo;
        this.templateType = templateType;
        this.desc = desc;
        this.ableStatus = ableStatus;
    }

    public String getSmsType() {
        return smsType;
    }

    public String getTemplateNo() {
        return templateNo;
    }

    public String getTemplateType() {
        return templateType;
    }

    public String getDesc() {
        return desc;
    }

    public AbleStatus getAbleStatus() {
        return ableStatus;
    }
}
