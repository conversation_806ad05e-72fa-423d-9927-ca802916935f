package com.maguo.loan.cash.flow.service.listener;


import com.maguo.loan.cash.flow.config.RabbitConfig;
import com.maguo.loan.cash.flow.service.AbstractListener;
import com.maguo.loan.cash.flow.service.LoanService;
import com.maguo.loan.cash.flow.service.MqService;
import com.maguo.loan.cash.flow.service.WarningService;
import com.rabbitmq.client.Channel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;

/**
 * <AUTHOR>
 * @date 2023/6/1 11:43
 */
@Component
public class LoanApplyListener extends AbstractListener {
    private static final Logger logger = LoggerFactory.getLogger(LoanApplyListener.class);
    public LoanApplyListener(MqService mqService, WarningService mqWarningService) {
        super(mqService, mqWarningService);
    }

    @Autowired
    private LoanService loanService;

    @RabbitListener(queues = RabbitConfig.Queues.LOAN_APPLY)
    public void loanApplyListen(Message message, Channel channel) {
        String loanId = new String(message.getBody(), StandardCharsets.UTF_8);
        try {
            logger.info("监听放款申请:{}", loanId);
            // service
            loanService.loanApply(loanId);
        } catch (Exception e) {
            getMqWarningService().warn("放款申请异常:loanId," + loanId + "," + e.getMessage(), msg -> logger.error(msg, e));
        } finally {
            ackMsg(loanId, message, channel);
        }
    }
}
