package com.maguo.loan.cash.flow.entity;

import jakarta.persistence.Entity;
import jakarta.persistence.Table;

@Entity
@Table(name = "user_pre_screening_query")
public class UserPreScreeningQuery  extends BaseEntity {
    //'订单号'
    private String orderNo;
    //业务类型1-前筛
    private String bizType;
    //''身份证号''
    private String cardNo;
    //''⼿机号''
    private String mobile;
    //'客户姓名'
    private String customName;
    //'前筛结果'
    private String userClassifyInfo;

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getBizType() {
        return bizType;
    }

    public void setBizType(String bizType) {
        this.bizType = bizType;
    }

    public String getCardNo() {
        return cardNo;
    }

    public void setCardNo(String cardNo) {
        this.cardNo = cardNo;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getCustomName() {
        return customName;
    }

    public void setCustomName(String customName) {
        this.customName = customName;
    }

    public String getUserClassifyInfo() {
        return userClassifyInfo;
    }

    public void setUserClassifyInfo(String userClassifyInfo) {
        this.userClassifyInfo = userClassifyInfo;
    }
}
