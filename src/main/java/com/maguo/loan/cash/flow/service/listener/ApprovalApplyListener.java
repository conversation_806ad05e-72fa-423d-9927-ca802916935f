package com.maguo.loan.cash.flow.service.listener;


import com.maguo.loan.cash.flow.entity.PreOrder;
import com.maguo.loan.cash.flow.repository.PreOrderRepository;
import com.maguo.loan.cash.flow.service.AbstractListener;
import com.maguo.loan.cash.flow.service.MqService;
import com.maguo.loan.cash.flow.service.WarningService;
import com.maguo.loan.cash.flow.service.approval.ApprovalService;
import com.rabbitmq.client.Channel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;

/**
 * 进件申请监听
 *
 * <AUTHOR>
 */
@Component
public class ApprovalApplyListener extends AbstractListener {

    private static final Logger logger = LoggerFactory.getLogger(ApprovalApplyListener.class);
    @Autowired
    private PreOrderRepository preOrderRepository;
    @Qualifier("warningService")
    @Autowired
    private WarningService warningService;

    public ApprovalApplyListener(MqService mqService, WarningService mqWarningService) {
        super(mqService, mqWarningService);
    }

    @Autowired
    private ApprovalService approvalApply;

    public void listenerApprovalApply(Message message, Channel channel) {
        String preOrderId = new String(message.getBody(), StandardCharsets.UTF_8);
        PreOrder preOrder = preOrderRepository.findById(preOrderId).orElseThrow();
        // 获取路由键
        String routingKey = message.getMessageProperties().getReceivedRoutingKey();

        try {
            logger.info("进件申请:{},routingKey:{}", preOrderId, routingKey);
            approvalApply.approvalApply(preOrderId);
        } catch (Exception e) {
            logger.error("进件申请异常:", e);
            warningService.warn("进件申请异常: flowChannel = " + preOrder.getFlowChannel() + " , preOrderId = " + preOrderId + "," + e.getMessage());
        } finally {
            ackMsg(preOrderId, message, channel);
        }
    }

}
