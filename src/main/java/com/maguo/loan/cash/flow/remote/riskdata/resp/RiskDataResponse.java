package com.maguo.loan.cash.flow.remote.riskdata.resp;

import com.alibaba.fastjson2.JSONObject;

import java.math.BigDecimal;

/**
* <AUTHOR>
* @Description 内部风控系统响应
* @Date 2025-05-23
* @Version 1.0
*/
public class RiskDataResponse {

    /**
     * 响应码 200 表示操作成功
     */
    private String code;
    /**
     * 描述
     */
    private String message;
    /**
     * 接口返回数据
     */
    private ResultWrapper result;
    /**
     * 链路追踪码
     */
    private String traceId;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public ResultWrapper getResult() {
        return result;
    }

    public void setResult(ResultWrapper result) {
        this.result = result;
    }

    public String getTraceId() {
        return traceId;
    }

    public void setTraceId(String traceId) {
        this.traceId = traceId;
    }

    public static class ResultWrapper {
        private String pipeline_id;
        /**
         * SUCCESS 成功   FAIL 失败
         */
        private String asyn_commit_status;

        public String getPipeline_id() {
            return pipeline_id;
        }

        public void setPipeline_id(String pipeline_id) {
            this.pipeline_id = pipeline_id;
        }

        public String getAsyn_commit_status() {
            return asyn_commit_status;
        }

        public void setAsyn_commit_status(String asyn_commit_status) {
            this.asyn_commit_status = asyn_commit_status;
        }
    }

    public static class InnerResult {
        /**
         * 上一节点分值型结果
         */
        private BigDecimal score;
        /**
         * 决策结果信息  通过，拒绝，人工审批，预警 ， 对应不同字段
         */
        private String decision;
        /**
         * 上一节点决策矩阵结果
         */
        private String matrix;
        /**
         * 自定义决策结果  内部的reason字段表示失败原因
         */
        private JSONObject custom_str;
        /**
         * 上一节点金额利率型结果
         */
        private Object money_rate;

        public BigDecimal getScore() {
            return score;
        }

        public void setScore(BigDecimal score) {
            this.score = score;
        }

        public String getDecision() {
            return decision;
        }

        public void setDecision(String decision) {
            this.decision = decision;
        }

        public String getMatrix() {
            return matrix;
        }

        public void setMatrix(String matrix) {
            this.matrix = matrix;
        }

        public JSONObject getCustom_str() {
            return custom_str;
        }

        public void setCustom_str(JSONObject custom_str) {
            this.custom_str = custom_str;
        }

        public Object getMoney_rate() {
            return money_rate;
        }

        public void setMoney_rate(Object money_rate) {
            this.money_rate = money_rate;
        }
    }


}
