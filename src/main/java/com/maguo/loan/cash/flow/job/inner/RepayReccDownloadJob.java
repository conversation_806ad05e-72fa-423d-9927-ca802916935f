package com.maguo.loan.cash.flow.job.inner;


import com.jinghang.common.util.JsonUtil;
import com.maguo.loan.cash.flow.enums.FileType;
import com.maguo.loan.cash.flow.job.AbstractJobHandler;
import com.maguo.loan.cash.flow.job.JobParam;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023/6/9
 */
@Component
@JobHandler("repayReccDownloadJob")
public class RepayReccDownloadJob extends AbstractJobHandler {

    private static final Logger logger = LoggerFactory.getLogger(RepayReccDownloadJob.class);

    @Autowired
    private RecDownloadFactory recDownloadFactory;


    @Override
    public void doJob(JobParam jobParam) {
        logger.info("repayReccDownloadJob jobParam:{}", JsonUtil.toJsonString(jobParam));
        LocalDate fileDate;

        if (Objects.nonNull(jobParam)) {
            fileDate = jobParam.getStartDate();
        } else {
            fileDate = LocalDate.now().minusDays(1L);
        }

        recDownloadFactory.getRecDownloadService(FileType.REPAYMENT_FILE).downLoad(fileDate);

        logger.info("repayReccDownloadJob finished");
    }

}
