package com.maguo.loan.cash.flow.entity;


import com.maguo.loan.cash.flow.enums.FlowChannel;
import com.maguo.loan.cash.flow.enums.ProtocolChannel;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Table;


/**
 * <AUTHOR>
 * @date 2023/9/15
 */
@Entity
@Table(name = "order_bind_card_record")
public class OrderBindCardRecord extends BaseEntity {

    private String userId;

    /**
     * 流量渠道
     */
    @Enumerated(EnumType.STRING)
    private FlowChannel flowChannel;

    /**
     * 第一绑卡渠道
     */
    @Enumerated(EnumType.STRING)
    private ProtocolChannel firstProtocolChannel;

    private String firstCardId;

    /**
     * 第二绑卡渠道
     */
    @Enumerated(EnumType.STRING)
    private ProtocolChannel secondProtocolChannel;

    private String secondCardId;

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public FlowChannel getFlowChannel() {
        return flowChannel;
    }

    public void setFlowChannel(FlowChannel flowChannel) {
        this.flowChannel = flowChannel;
    }

    public ProtocolChannel getFirstProtocolChannel() {
        return firstProtocolChannel;
    }

    public void setFirstProtocolChannel(ProtocolChannel firstProtocolChannel) {
        this.firstProtocolChannel = firstProtocolChannel;
    }

    public String getFirstCardId() {
        return firstCardId;
    }

    public void setFirstCardId(String firstCardId) {
        this.firstCardId = firstCardId;
    }

    public ProtocolChannel getSecondProtocolChannel() {
        return secondProtocolChannel;
    }

    public void setSecondProtocolChannel(ProtocolChannel secondProtocolChannel) {
        this.secondProtocolChannel = secondProtocolChannel;
    }

    public String getSecondCardId() {
        return secondCardId;
    }

    public void setSecondCardId(String secondCardId) {
        this.secondCardId = secondCardId;
    }
}
