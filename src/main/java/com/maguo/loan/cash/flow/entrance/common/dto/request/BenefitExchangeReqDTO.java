package com.maguo.loan.cash.flow.entrance.common.dto.request;

import jakarta.validation.constraints.NotBlank;

import java.io.Serial;
import java.io.Serializable;

/**
 * 获取权益兑换链接
 *
 * <AUTHOR>
 * @date 2024/9/11
 */
public class BenefitExchangeReqDTO implements Serializable {
    @Serial
    private static final long serialVersionUID = 6466926162604611593L;

    /**
     * 合作机构单号
     */
    @NotBlank
    private String partnerOrderNo;

    public String getPartnerOrderNo() {
        return partnerOrderNo;
    }

    public void setPartnerOrderNo(String partnerOrderNo) {
        this.partnerOrderNo = partnerOrderNo;
    }
}
