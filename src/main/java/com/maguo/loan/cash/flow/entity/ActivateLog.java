package com.maguo.loan.cash.flow.entity;


import com.maguo.loan.cash.flow.enums.ApplyChannel;
import com.maguo.loan.cash.flow.enums.WhetherState;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Table;

/**
 * <AUTHOR>
 * @date 2024/7/4
 */
@Entity
@Table(name = "activate_log")
public class ActivateLog extends BaseEntity {

    @Enumerated(EnumType.STRING)
    private ApplyChannel applyChannel;

    @Enumerated(EnumType.STRING)
    private WhetherState activateState;

    private String msg;

    private String mobile;

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public WhetherState getActivateState() {
        return activateState;
    }

    public void setActivateState(WhetherState activateState) {
        this.activateState = activateState;
    }

    public ApplyChannel getApplyChannel() {
        return applyChannel;
    }

    public void setApplyChannel(ApplyChannel applyChannel) {
        this.applyChannel = applyChannel;
    }

    @Override
    protected String prefix() {
        return "AL";
    }
}
