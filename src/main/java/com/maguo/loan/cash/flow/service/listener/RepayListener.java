package com.maguo.loan.cash.flow.service.listener;


import com.maguo.loan.cash.flow.config.RabbitConfig;
import com.maguo.loan.cash.flow.service.AbstractListener;
import com.maguo.loan.cash.flow.service.MqService;
import com.maguo.loan.cash.flow.service.RepayService;
import com.maguo.loan.cash.flow.service.WarningService;
import com.rabbitmq.client.Channel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;

/**
 * @Classname RepayListener
 * @Description 还款申请
 * @Date 2023/10/18 10:48
 * @Created by gale
 */
@Component
public class RepayListener extends AbstractListener {
    private static final Logger logger = LoggerFactory.getLogger(RepayListener.class);

    @Autowired
    private WarningService warningService;
    @Autowired
    private RepayService repayService;

    public RepayListener(MqService mqService, WarningService mqWarningService) {
        super(mqService, mqWarningService);
    }

    @RabbitListener(queues = RabbitConfig.Queues.REPAY_APPLY)
    public void listenRepayApply(Message message, Channel channel) {
        String repayId = new String(message.getBody(), StandardCharsets.UTF_8);
        try {
            logger.info("还款申请core:{}", repayId);
            repayService.repay(repayId);
            // service
        } catch (Exception e) {
            warningService.warn("还款申请core异常:" + repayId + "," + e.getMessage(), msg -> logger.error("还款申请core异常:{},", repayId, e));
        } finally {
            ackMsg(repayId, message, channel);
        }
    }
}
