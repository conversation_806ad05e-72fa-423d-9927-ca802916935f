package com.maguo.loan.cash.flow.job.ppd;

import com.alibaba.fastjson2.JSON;
import com.jinghang.capital.api.dto.BankChannel;
import com.jinghang.capital.api.dto.FileType;
import com.jinghang.capital.api.dto.ProcessStatus;
import com.jinghang.capital.api.dto.Product;
import com.jinghang.capital.api.dto.RestResult;
import com.jinghang.capital.api.dto.file.FileDownloadDto;
import com.jinghang.capital.api.dto.file.FileDownloadResultDto;
import com.jinghang.cash.api.dto.ProjectAgreementDto;
import com.jinghang.cash.api.enums.ActiveInactive;
import com.jinghang.cash.api.enums.TemplateOwner;
import com.jinghang.common.util.CollectionUtil;
import com.jinghang.common.util.JsonUtil;
import com.maguo.loan.cash.flow.convert.EnumConvert;
import com.maguo.loan.cash.flow.entity.AgreementSignRelation;
import com.maguo.loan.cash.flow.entity.AgreementSignatureRecord;
import com.maguo.loan.cash.flow.entity.Loan;
import com.maguo.loan.cash.flow.entity.Order;
import com.maguo.loan.cash.flow.entity.UserFile;
import com.maguo.loan.cash.flow.entrance.ppd.config.PpdConfig;
import com.maguo.loan.cash.flow.enums.FlowChannel;
import com.maguo.loan.cash.flow.enums.LoanStage;
import com.maguo.loan.cash.flow.enums.ProcessState;
import com.maguo.loan.cash.flow.enums.WhetherState;
import com.maguo.loan.cash.flow.job.AbstractJobHandler;
import com.maguo.loan.cash.flow.job.JobParam;
import com.maguo.loan.cash.flow.job.agreement.LoanAgreementJob;
import com.maguo.loan.cash.flow.remote.core.FinLoanFileService;
import com.maguo.loan.cash.flow.remote.manage.ProjectAgreementFeign;
import com.maguo.loan.cash.flow.repository.AgreementSignRelationRepository;
import com.maguo.loan.cash.flow.repository.AgreementSignatureRecordRepository;
import com.maguo.loan.cash.flow.repository.LoanRepository;
import com.maguo.loan.cash.flow.repository.OrderRepository;
import com.maguo.loan.cash.flow.repository.UserFileRepository;
import com.maguo.loan.cash.flow.service.FileService;
import com.maguo.loan.cash.flow.service.JHReconService;
import com.maguo.loan.cash.flow.service.WarningService;
import com.maguo.loan.cash.flow.util.SftpUtils;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @作者 Mr.sandman
 * @时间 2025/06/26 16:51
 */
@Component
@JobHandler(value = "ppdSignJob")
public class PPDSignJob extends AbstractJobHandler {

    private static final Logger logger = LoggerFactory.getLogger(PPDSignJob.class);

    @Autowired
    private SftpUtils sftpUtils;

    @Autowired
    private PpdConfig ppdConfig;
    @Autowired
    private LoanAgreementJob loanAgreementJob;
    @Autowired
    private OrderRepository orderRepository;

    @Autowired
    private LoanRepository loanRepository;

    @Autowired
    private FinLoanFileService finLoanFileService;

    @Autowired
    private UserFileRepository userFileRepository;

    @Autowired
    private AgreementSignRelationRepository agreementSignRelationRepository;

    @Autowired
    private AgreementSignatureRecordRepository agreementSignatureRecordRepository;

    @Autowired
    private FileService fileService;

    @Autowired
    private JHReconService jhReconService;

    @Autowired
    private ProjectAgreementFeign projectAgreementFeign;

    @Value(value = "${oss.bucket.name}")
    private String ossBucket;

    private static final String PP_CONTRACT_NAME = "loanReqNo";

    private static final String JOB_NAME = "ppdSignJob";

    @Autowired
    private WarningService warningService;

    @Override
    public void doJob(JobParam jobParam) {
        logger.info("{} 任务启动，参数: {}", JOB_NAME, JsonUtil.toJsonString(jobParam));
        //校验并预处理任务参数
        validateAndProcessJobParam(jobParam);
        //根据参数拉取需要处理的借据列表
        List<Loan> list = fetchLoans(jobParam);
        if (CollectionUtils.isEmpty(list)) {
            logger.info("{} 未找到符合条件的放款成功借据，任务结束。", JOB_NAME);
            return;
        }
        logger.info("{} 成功拉取 {} 条借据进行处理。", JOB_NAME, list.size());
        //获取所有放款单据对应的project-code
        Set<String> projectCodes = list.stream().map(Loan::getProjectCode).collect(Collectors.toSet());
        //回传流量的协议配置
        List<ProjectAgreementDto> agreementToFlowDtoList = projectAgreementFeign.getByReturnStatus(projectCodes.stream().toList(), ActiveInactive.Y.getCode(), "");
        //归属方属于资方的合同协议列表
        List<ProjectAgreementDto> agreementToCapitalDtoList = agreementToFlowDtoList.stream().filter(dto -> TemplateOwner.CAPITAL.name().equals(dto.getTemplateOwner())).toList();
        //所有文件name和对应的文件合同名称
        Map<String, ProjectAgreementDto> filenameFlowMaps = agreementToFlowDtoList.stream().collect(Collectors.toMap(agreementDto -> agreementDto.getProjectCode() + agreementDto.getContractTemplateType().name(), Function.identity()));
        //需要获取资方合同协议的文件合同名称
        Map<String, ProjectAgreementDto> filenameCapitalMaps = agreementToCapitalDtoList.stream().collect(Collectors.toMap(agreementDto -> agreementDto.getProjectCode() + agreementDto.getContractTemplateType().name(), Function.identity()));
        //开始拉取
        logger.info("借款合同下载,loan size:{}", list.size());
        list.forEach(loan -> processSingleLoan(loan, filenameFlowMaps, filenameCapitalMaps));
        //回调
        jhReconService.saveTaskMonitoringData(jobParam, true, null);
        logger.info("{} 任务执行完成。", JOB_NAME);
    }

    /**
     * 处理协议文件上传下载方法
     * @param loan 放款记录
     * @param filenameFlowMaps 所有文件name和对应的文件合同名称集合
     * @param filenameCapitalMaps 资方合同协议的文件合同名称集合
     */
    private void processSingleLoan(Loan loan, Map<String, ProjectAgreementDto> filenameFlowMaps, Map<String, ProjectAgreementDto> filenameCapitalMaps) {
        logger.info("开始处理协议 loanId:{}", loan.getId());
        try {
            // 处理资方合同下载
            processCapitalAgreements(loan, filenameCapitalMaps);
            // 处理流量协议上传
            processFlowAgreements(loan, filenameFlowMaps);
            // 处理资金系统协议上传
            processCapitalSystemAgreements(loan, filenameCapitalMaps);
        } catch (Exception e) {
            logger.error("处理贷款协议时发生异常 loanId:{}", loan.getId(), e);
        }
    }

    /**
     * 处理流量协议上传
     * @param loan 放款记录
     * @param filenameFlowMaps 所有文件name和对应的文件合同名称集合
     */
    private void processFlowAgreements(Loan loan, Map<String, ProjectAgreementDto> filenameFlowMaps) {
        Order order = orderRepository.findOrderById(loan.getOrderId());
        //复借场景需查询客户首借时传输给资金方的协议（综合授权书、数字证书使用授权协议 ），复制一份到复借对应的授信编号的路径下
        loanAgreementJob.handleReloanProtocolFiles(loan);
        List<AgreementSignatureRecord> agreementList = agreementSignatureRecordRepository.findByRiskIdAndSignState(order.getRiskId(), ProcessState.SUCCEED);
        if (CollectionUtils.isEmpty(agreementList)) {
            return;
        }
        agreementList.forEach(record -> {
            try {
                //获取文件名称规则
                String fileName = filenameFlowMaps.get(loan.getProjectCode() +
                    record.getFileType().name()).getCapitalContractName();//文件名称
                //处理流量系统协议文件上传至sftp
                uploadFlowFileToSftp(loan, record.getCommonOssUrl(), fileName);
            } catch (Exception e) {
                warningService.warn("上传资方协议到SFTP失败,loanId:" + loan.getId() + "recordId:" + record.getId() + "msg:" + e.getMessage(), logger::error);
            }
        });
    }

    /**
     * 处理资金系统协议上传
     * @param loan 放款记录
     * @param filenameCapitalMaps 资方合同协议的文件合同名称集合
     */
    private void processCapitalSystemAgreements(Loan loan, Map<String, ProjectAgreementDto> filenameCapitalMaps) {
        Order order = orderRepository.findOrderById(loan.getOrderId());
        List<UserFile> userFileList = userFileRepository.findByUserIdAndLoanNo(order.getUserId(), loan.getId());
        logger.info("合同文件数量:{}, 内容如下:", userFileList.size());
        if (CollectionUtils.isEmpty(userFileList)) {
            return;
        }
        userFileList.stream().filter(file -> !FileType.ID_HEAD.name().equals(file.getFileType().name()) ||
            !FileType.ID_NATION.name().equals(file.getFileType().name()) ||
            !FileType.ID_FACE.name().equals(file.getFileType().name())
        ).forEach(userFile -> {
            try {
                logger.info(" - fileId:{}, fileType:{}, fileName:{}, ossKey:{}",
                    userFile.getId(), userFile.getFileType(), userFile.getFileName(), userFile.getOssKey());
                //获取资金方合同名称规则
                String fileName = filenameCapitalMaps.get(loan.getProjectCode() +
                    userFile.getFileType().name()).getCapitalContractName();//文件名称
                //处理资金系统协议文件上传至sftp
                uploadCapitalFileToSftp(loan, userFile, fileName);
            } catch (Exception e) {
                warningService.warn("上传资方协议到SFTP失败,loanId:" + loan.getId() + "fileId:" + userFile.getId() + "msg:" + e.getMessage(), logger::error);
            }
        });
    }

    /**
     * 处理资方合同下载
     * @param loan 放款记录
     * @param filenameCapitalMaps 资方合同协议的文件合同名称集合
     */
    private void processCapitalAgreements(Loan loan, Map<String, ProjectAgreementDto> filenameCapitalMaps) {
        //获取文件类型集合
        List<FileType> fileTypes = getCapitalFileTypes(loan, filenameCapitalMaps);
        if (CollectionUtils.isEmpty(fileTypes)) {
            return;
        }
        Map<String, String> existingOssMap = getExistingOssFiles(loan);
        fileTypes.forEach(fileType -> {
            try {
                Optional<FileDownloadResultDto> downloadResult = downloadAgreementFile(loan, fileType);
                downloadResult.ifPresent(result -> {
                    if (isFileAlreadyDownloaded(result, existingOssMap)) {
                        logger.info("借据:{}, 跳过已下载文件:{}", loan.getId(), fileType);
                        return;
                    }
                    saveUserFile(loan, fileType, result);
                });
            } catch (Exception e) {
                warningService.warn("下载资方合同异常 loanId:" + loan.getId() + "fileType:" + fileType + "msg:" + e.getMessage(), logger::error);
            }
        });
    }

    /**
     * 获取文件类型集合
     * @param loan 放款记录
     * @param filenameCapitalMaps 资方合同协议的文件合同名称集合
     * @return 文件类型集合
     */
    private List<FileType> getCapitalFileTypes(Loan loan, Map<String, ProjectAgreementDto> filenameCapitalMaps) {
        return filenameCapitalMaps.keySet().stream().filter(key -> key.startsWith(loan.getProjectCode())).map(key -> FileType.valueOf(key.replace(loan.getProjectCode(), ""))).collect(Collectors.toList());
    }

    private Map<String, String> getExistingOssFiles(Loan loan) {
        List<UserFile> userFiles = agreementSignRelationRepository.queryUserFiles(Optional.ofNullable(loan.getLoanRecordId()).orElse(loan.getId()));
        return CollectionUtil.isEmpty(userFiles) ? Collections.emptyMap() : userFiles.stream().collect(Collectors.toMap(UserFile::getOssKey, UserFile::getOssBucket, (k1, k2) -> k2));
    }

    /**
     * 下载合同文件
     * @param loan 放款记录
     * @param fileType 文件类型
     * @return 下载合同后返回的结果信息
     */
    private Optional<FileDownloadResultDto> downloadAgreementFile(Loan loan, FileType fileType) {
        FileDownloadDto fileDownloadDto = new FileDownloadDto();
        fileDownloadDto.setLoanId(loan.getLoanNo());
        fileDownloadDto.setLoanOrderId(Optional.ofNullable(loan.getLoanRecordId()).orElse(loan.getId()));
        fileDownloadDto.setType(fileType);
        fileDownloadDto.setProduct(Product.ZC_CASH);
        fileDownloadDto.setBankChannel(loan.getBankChannel());
        RestResult<FileDownloadResultDto> restResult = finLoanFileService.download(fileDownloadDto);
        logger.info("下载合同文件结果 loanId:{}, fileType:{}, result:{}", loan.getId(), fileType, JSON.toJSONString(restResult));
        if (!restResult.isSuccess() || Objects.isNull(restResult.getData())
            || StringUtils.isAnyBlank(restResult.getData().getOssBucket(), restResult.getData().getOssPath())) {
            logger.info("合同文件不存在或下载失败 loanId:{}, fileType:{}", loan.getId(), fileType);
            return Optional.empty();
        }
        return Optional.of(restResult.getData());
    }

    /**
     * 检验合同文件是否已下载过
     * @return 检验结果
     */
    private boolean isFileAlreadyDownloaded(FileDownloadResultDto result, Map<String, String> existingOssMap) {
        return existingOssMap.containsKey(result.getOssPath()) && existingOssMap.get(result.getOssPath()).equals(result.getOssBucket());
    }

    /**
     * 根据Job参数从数据库获取需要处理的借据列表
     * @param jobParam 任务参数
     * @return 借据列表
     */
    private List<Loan> fetchLoans(JobParam jobParam) {
        if (CollectionUtil.isNotEmpty(jobParam.getLoanIds())) {
            logger.info("{} loanIds size: {}", JOB_NAME, jobParam.getLoanIds().size());
            return loanRepository.findAllById(jobParam.getLoanIds());
        }
        LocalDateTime startDateTime = LocalDateTime.of(jobParam.getStartDate(), LocalTime.MIN);
        LocalDateTime endDateTime = LocalDateTime.of(jobParam.getEndDate(), LocalTime.MAX);
        if (Objects.nonNull(jobParam.getBankChannel())) {
            logger.info("{} 指定资方: {}, 开始时间: {}, 结束时间: {}", JOB_NAME, jobParam.getBankChannel(), jobParam.getStartDate(), jobParam.getEndDate());
            return loanRepository.findByLoanStateAndBankChannelAndLoanTimeBetweenAndFlowChannel(ProcessState.SUCCEED, jobParam.getBankChannel(), startDateTime, endDateTime, FlowChannel.PPCJDL);
        } else {
            logger.info("{} 指定开始时间: {}, 结束时间: {}", JOB_NAME, jobParam.getStartDate(), jobParam.getEndDate());
            return loanRepository.findByLoanStateAndLoanTimeBetweenAndFlowChannel(ProcessState.SUCCEED, startDateTime, endDateTime, FlowChannel.PPCJDL);
        }
    }

    /**
     * 校验并设置Job参数的默认值
     * @param jobParam 任务参数
     * @throws IllegalArgumentException 如果参数不合法
     */
    private void validateAndProcessJobParam(JobParam jobParam) {
        logger.info("ppdSignJob jobParam:{}", JsonUtil.toJsonString(jobParam));
        LocalDate fileDate = jobParam.getStartDate();
        LocalDate endDate = jobParam.getEndDate();
        if (Objects.isNull(fileDate)) {
            fileDate = LocalDate.now().minusDays(1);
        }
        if (Objects.isNull(endDate)) {
            endDate = fileDate;
        }
        jobParam.setTaskHandler("loanAgreementJob");
        jobParam.setTaskDescription("协议文件上传-绿信");
        jobParam.setStartDate(fileDate);//开始时间
        jobParam.setEndDate(endDate);//结束时间
    }

    /**
     * 处理流量系统协议文件上传至sftp
     * @param loan 放款记录
     * @param ossKey 公共返回地址
     * @param fileName 文件名称
     */
    public void uploadFlowFileToSftp(Loan loan, String ossKey, String fileName) {
        fileService.getOssFile(ossBucket, ossKey, inputStream -> {
            try {
                String sftpPath;//文件上传至sftp路径
                //获取合同配置的文件名称替换成外部放款单号
                String newFileName = fileName.replace(PP_CONTRACT_NAME, loan.getOuterLoanId());
                //生成包含放款时间、外部借据ID的路径字符串
                String remoteDir = generateLoanTimeFilePath(loan);
                // 根据资方渠道区分不同的sftp账号
                if (Objects.equals(loan.getBankChannel(),BankChannel.CYBK)) {//长银
                    sftpPath = ppdConfig.getSftpDownloadPath() + remoteDir;
                    logger.info("上传文件——长银SFTP, fileName:{}, remotePath:{}", newFileName, sftpPath);
                    sftpUtils.uploadStreamToPPCJDLSftp(inputStream, newFileName, sftpPath);
                } else if (Objects.equals(loan.getBankChannel(),BankChannel.HXBK)) {//湖消
                    sftpPath = ppdConfig.getSftpHxDownloadPath() + remoteDir;
                    logger.info("上传文件——湖消SFTP, fileName:{}, remotePath:{}", newFileName, sftpPath);
                    sftpUtils.uploadStreamToPPCJDLSftpHx(inputStream, newFileName, sftpPath);
                }
                logger.info("协议文件上传拍拍sftp成功");
            } catch (Exception e) {
                logger.error("协议文件上传绿信sftp失败:", e);
                throw new RuntimeException(e);
            }
        });
    }

    /**
     * 处理资金系统协议文件上传至sftp
     * @param loan 放款记录
     * @param userFile 用户文件信息记录
     * @param fileName 文件名称
     * @return 文件上传至sftp路径
     */
    public String uploadCapitalFileToSftp(Loan loan, UserFile userFile, String fileName) {
        AtomicReference<String> result = new AtomicReference<>("");
        fileService.getOssFile(userFile.getOssBucket(), userFile.getOssKey(), inputStream -> {
            try {
                String sftpPath;//文件上传至sftp路径
                //获取合同配置的文件名称替换成外部放款单号
                String newFileName = fileName.replace(PP_CONTRACT_NAME, loan.getOuterLoanId());
                //生成包含放款时间、外部借据ID的路径字符串
                String remoteDir = generateLoanTimeFilePath(loan);
                // 根据资方渠道区分不同的sftp账号
                if (Objects.equals(loan.getBankChannel(),BankChannel.CYBK)) {//长银
                    sftpPath = ppdConfig.getSftpDownloadPath() + remoteDir;
                    result.set(sftpPath);
                    logger.info("上传文件——长银SFTP, fileName:{}, remotePath:{}", newFileName, sftpPath);
                    sftpUtils.uploadStreamToPPCJDLSftp(inputStream, newFileName, sftpPath);
                } else if (Objects.equals(loan.getBankChannel(),BankChannel.HXBK)) {//湖消
                    sftpPath = ppdConfig.getSftpHxDownloadPath() + remoteDir;
                    result.set(sftpPath);
                    logger.info("上传文件——湖消SFTP, fileName:{}, remotePath:{}", newFileName, sftpPath);
                    sftpUtils.uploadStreamToPPCJDLSftpHx(inputStream, newFileName, sftpPath);
                }
                logger.info("协议文件上传拍拍sftp成功");
            } catch (Exception e) {
                logger.error("协议文件上传拍拍sftp失败:", e);
                throw new RuntimeException(e);
            }
        });
        return result.get();
    }

    /**
     * 生成包含放款时间、外部借据ID的路径字符串
     * @param loan 包含放款时间和外部借据ID的对象
     * @return 格式化后的字符串
     */
    private String generateLoanTimeFilePath(Loan loan) {
        if (Objects.isNull(loan) || Objects.isNull(loan.getLoanTime()) || StringUtils.isBlank(loan.getOuterLoanId())) {
            return "";
        }
        String formattedDate = formatLocalDateTime(loan.getLoanTime());
        String outerLoanId = loan.getOuterLoanId();
        logger.info("generateLoanTimeFilePath:{}", formattedDate + "/" + outerLoanId + "/");
        return formattedDate + "/" + outerLoanId + "/";
    }

    /**
     * 将 LocalDateTime 转换为格式为 "yyyyMMdd" 的字符串
     * @param dateTime LocalDateTime 对象
     * @return 格式化后的字符串
     */
    private static String formatLocalDateTime(LocalDateTime dateTime) {
        if (Objects.isNull(dateTime)) {
            return null;
        }
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
        return dateTime.format(formatter);
    }

    private void saveUserFile(Loan loan, FileType fileType, FileDownloadResultDto resultData) {
        UserFile userFile = new UserFile();
        userFile.setUserId(loan.getUserId());
        LoanStage loanStage = getLoanStage(fileType);
        userFile.setLoanStage(loanStage);
        userFile.setLoanNo(loan.getId());
        com.maguo.loan.cash.flow.enums.FileType cashFileType = EnumConvert.INSTANCE.toCoreApi(fileType);
        userFile.setFileType(cashFileType);
        String fileName = StringUtils.isNotBlank(resultData.getFileName())
            ? resultData.getFileName() : Optional.ofNullable(cashFileType).map(com.maguo.loan.cash.flow.enums.FileType::getDesc).orElse("");
        userFile.setFileName("资金-" + fileName);
        userFile.setOssBucket(resultData.getOssBucket());
        userFile.setOssKey(resultData.getOssPath());
        userFile.setSignFinal(ProcessStatus.SUCCESS == resultData.getFileStatus() ? WhetherState.Y : WhetherState.N);
        userFileRepository.save(userFile);

        AgreementSignRelation agreementSignRelation = new AgreementSignRelation();
        agreementSignRelation.setLoanStage(loanStage);
        agreementSignRelation.setSignApplyId(userFile.getId());
        agreementSignRelation.setUserId(loan.getUserId());
        agreementSignRelation.setOrderId(loan.getOrderId());
        if (loanStage == LoanStage.CREDIT) {
            agreementSignRelation.setRelatedId(loan.getCreditId());
        } else {
            agreementSignRelation.setRelatedId(Optional.ofNullable(loan.getLoanRecordId()).orElse(loan.getId()));
        }
        agreementSignRelationRepository.save(agreementSignRelation);
    }

    private LoanStage getLoanStage(FileType fileType) {
        return switch (fileType) {
            case CREDIT_APPLY, PERSONAL_CREDIT_AUTHORIZATION_LETTER, PROMISE_NOT_STUDENT,
                PERSONAL_INFORMATION_QUERY_LETTER -> LoanStage.CREDIT;
            default -> LoanStage.LOAN;
        };
    }
}
