package com.maguo.loan.cash.flow.service.listener;


import com.maguo.loan.cash.flow.config.RabbitConfig;
import com.maguo.loan.cash.flow.service.AbstractListener;
import com.maguo.loan.cash.flow.service.MqService;
import com.maguo.loan.cash.flow.service.RepayService;
import com.maguo.loan.cash.flow.service.WarningService;
import com.rabbitmq.client.Channel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;

/**
 * 还款通知监听
 */
@Component
public class RepayNotifyListener extends AbstractListener {
    private static final Logger logger = LoggerFactory.getLogger(RepayNotifyListener.class);

    public RepayNotifyListener(MqService mqService, WarningService mqWarningService) {
        super(mqService, mqWarningService);
    }

    @Autowired
    private RepayService repayService;

    @RabbitListener(queues = RabbitConfig.Queues.REPAY_NOTIFY)
    public void listenRepayNotify(Message message, Channel channel) {
        String repayId = new String(message.getBody(), StandardCharsets.UTF_8);
        try {
            logger.info("还款后通知core:{}", repayId);
            repayService.notifyCore(repayId);
        } catch (Exception e) {
            processException(repayId, message, e, "还款通知core异常", getMqService()::submitRepayNotifyDelay);
        } finally {
            ackMsg(repayId, message, channel);
        }
    }
}
