package com.maguo.loan.cash.flow.service.listener;


import com.maguo.loan.cash.flow.config.RabbitConfig;
import com.maguo.loan.cash.flow.service.AbstractListener;
import com.maguo.loan.cash.flow.service.MqService;
import com.maguo.loan.cash.flow.service.OrderRouterService;
import com.maguo.loan.cash.flow.service.WarningService;
import com.rabbitmq.client.Channel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;

/**
 * <AUTHOR>
 * @date 2023/12/25
 */
@Component
public class CreditRouteApplyListener extends AbstractListener {
    private static final Logger logger = LoggerFactory.getLogger(CreditRouteApplyListener.class);

    public CreditRouteApplyListener(MqService mqService, WarningService mqWarningService) {
        super(mqService, mqWarningService);
    }

    private OrderRouterService orderRouterService;

    @RabbitListener(queues = RabbitConfig.Queues.CREDIT_ROUTE_APPLY)
    public void loanApplyListen(Message message, Channel channel) {
        String orderId = new String(message.getBody(), StandardCharsets.UTF_8);
        try {
            logger.info("订单发起授信路由:{}", orderId);
            // service
            orderRouterService.router(orderId);
        } catch (Exception e) {
            getMqWarningService().warn("订单路由异常:" + e.getMessage(), msg -> logger.error(msg, e));
        } finally {
            ackMsg(orderId, message, channel);
        }
    }

    @Autowired
    public void setOrderRouterService(OrderRouterService orderRouterService) {
        this.orderRouterService = orderRouterService;
    }
}
