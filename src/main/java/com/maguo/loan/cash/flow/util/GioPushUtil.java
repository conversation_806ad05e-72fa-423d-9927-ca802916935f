package com.maguo.loan.cash.flow.util;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateDeserializer;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateSerializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalTimeSerializer;

import com.maguo.loan.cash.flow.enums.Education;
import com.maguo.loan.cash.flow.enums.Marriage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import com.fasterxml.jackson.core.type.TypeReference;


import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.Period;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.IntStream;

public class GioPushUtil {
    private static final Logger logger = LoggerFactory.getLogger(GioPushUtil.class);


    private static final int ID_CARD_LENGTH = 18;
    private static final int BIRTH_DATE_START = 6;
    private static final int BIRTH_DATE_END = 14;
    private static final String DATE_FORMAT = "yyyyMMdd";
    private static final int GENDER_CODE_POSITION = 16;

    /**
     * 根据身份证号计算年龄
     *
     * @param idCard 18位身份证号
     * @return 年龄
     */
    public static int calculateAgeFromIDCard(String idCard) {
        try {
            if (idCard == null || idCard.length() != ID_CARD_LENGTH) {
                logger.info("身份证号格式不正确{}", idCard);
                return 0;
            }
            String birthDateStr = idCard.substring(BIRTH_DATE_START, BIRTH_DATE_END);
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern(DATE_FORMAT);
            LocalDate birthDate = LocalDate.parse(birthDateStr, formatter);
            LocalDate currentDate = LocalDate.now();
            return Period.between(birthDate, currentDate).getYears();
        } catch (Exception e) {
            logger.error("解析身份证号时发生异常: {}", idCard, e);
            return 0;
        }
    }

    public static String getChineseMarriage(Marriage marriage) {
        if (Objects.isNull(marriage)) {
            return "未知";
        }
        switch (marriage) {
            case UNMARRIED:
                return "未婚";
            case MARRIED:
                return "已婚";
            case DIVORCED:
                return "离异";
            case WIDOWED:
                return "丧偶";
            case UNKNOWN:
            default:
                return "未知";
        }
    }

    public static String getChineseEducation(Education education) {
        if (Objects.isNull(education)) {
            return "未知";
        }
        switch (education) {
            case PRIMARY_SCHOOL:
                return "小学";
            case JUNIOR_HIGH_SCHOOL:
                return "初中";
            case HIGH_SCHOOL:
                return "高中";
            case JUNIOR_COLLEGE:
                return "专科";
            case COLLEGE:
                return "本科";
            case MASTER:
                return "硕士研究生";
            case DOCTOR:
                return "博士";
            case UNKNOWN:
            default:
                return "未知";
        }
    }

    /**
     * 根据身份证号计算性别
     */
    public static String getGenderFromIDCard(String idCard) {
        try {
            if (idCard == null || idCard.length() != ID_CARD_LENGTH) {
                logger.info("身份证号格式不正确{}", idCard);
                return "男";
            }
            int genderCode = Integer.parseInt(idCard.substring(GENDER_CODE_POSITION, GENDER_CODE_POSITION + 1));
            return genderCode % 2 == 0 ? "女" : "男";
        } catch (Exception e) {
            logger.error("解析身份证号时发生异常: {}", idCard, e);
            return "男";
        }
    }


    public static <T> String getMessageJson(T object) {
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        // 支持java8日期格式 localDate、localDateTime、localTime
        JavaTimeModule javaTimeModule = new JavaTimeModule();
        javaTimeModule.addSerializer(LocalDate.class, new LocalDateSerializer(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        javaTimeModule.addSerializer(LocalDateTime.class, new LocalDateTimeSerializer(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        javaTimeModule.addSerializer(LocalTime.class, new LocalTimeSerializer(DateTimeFormatter.ofPattern("HH:mm:ss")));
        objectMapper.registerModule(javaTimeModule);
        String jsonString = null;
        try {
            jsonString = objectMapper.writeValueAsString(object);
        } catch (JsonProcessingException e) {
            logger.error("json格式化异常", e);
            throw new RuntimeException(e);
        }
        return jsonString;
    }

    public static List<String> fromJsonToListOfString(String jsonString) {
        ObjectMapper objectMapper = new ObjectMapper();
        // 支持java8日期格式 localDate、localDateTime、localTime
        JavaTimeModule javaTimeModule = new JavaTimeModule();
        javaTimeModule.addDeserializer(LocalDate.class, new LocalDateDeserializer(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        javaTimeModule.addDeserializer(LocalDateTime.class, new LocalDateTimeDeserializer(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        javaTimeModule.addDeserializer(LocalTime.class, new LocalTimeDeserializer(DateTimeFormatter.ofPattern("HH:mm:ss")));
        objectMapper.registerModule(javaTimeModule);

        List<String> list = null;
        try {
            list = objectMapper.readValue(jsonString, new TypeReference<List<String>>() {
            });
        } catch (Exception e) {
            logger.error("json反序列化异常", e);
            throw new RuntimeException(e);
        }
        return list;
    }


    public static <T> List<T> batchQuery(List<String> allAddressCodes, int batchSize, Function<List<String>, List<T>> queryFunction) {
        List<T> result = new ArrayList<>();
        int totalSize = allAddressCodes.size();
        int totalPages = (totalSize + batchSize - 1) / batchSize;

        for (int i = 0; i < totalPages; i++) {
            int start = i * batchSize;
            int end = Math.min(start + batchSize, totalSize);
            List<String> batch = allAddressCodes.subList(start, end);
            result.addAll(queryFunction.apply(batch));
        }

        return result;
    }

    public static <T> List<List<T>> splitList(List<T> list, int batchSize) {
        int totalSize = list.size();
        int fullBatches = totalSize / batchSize;
        int remainder = totalSize % batchSize;

        List<List<T>> result = new ArrayList<>();

        IntStream.range(0, fullBatches).forEach(i ->
            result.add(list.subList(i * batchSize, (i + 1) * batchSize))
        );

        if (remainder > 0) {
            result.add(list.subList(fullBatches * batchSize, totalSize));
        }

        return result;
    }

}
