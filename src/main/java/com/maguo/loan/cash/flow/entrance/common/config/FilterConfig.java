package com.maguo.loan.cash.flow.entrance.common.config;

import com.maguo.loan.cash.flow.entrance.common.filter.ReqHeaderFilter;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date 2024/9/10
 */
@Configuration("commonFilterConfig")
public class FilterConfig {

    @Bean
    public FilterRegistrationBean<ReqHeaderFilter> customFilterRegistration() {
        FilterRegistrationBean<ReqHeaderFilter> registrationBean = new FilterRegistrationBean<>();
        registrationBean.setFilter(new ReqHeaderFilter());
        registrationBean.addUrlPatterns("/api/*");
        registrationBean.setOrder(1);
        return registrationBean;
    }
}
