package com.maguo.loan.cash.flow.entity;

import com.maguo.loan.cash.flow.enums.WhetherState;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Table;

import java.math.BigDecimal;


/**
 * <AUTHOR>
 */
@Entity
@Table(name = "lvxin_repay_apply_record")
public class LvxinRepayApplyRecord extends BaseEntity {

    @Override
    //protected String prefix() {
    //    return "HY";
    //}
    protected String prefix() {
        return "LX";
    }

    private String loanId;

    private String outLoanId;

    private String outCreditId;

    private String outRepayId;

    private String repayCardId;

    private String periods;

    @Enumerated(EnumType.STRING)
    private WhetherState needSmsCode;

    private BigDecimal consultationFeeWaiver;

    private BigDecimal penaltyInterestWaiver;

    public String getLoanId() {
        return loanId;
    }

    public void setLoanId(String loanId) {
        this.loanId = loanId;
    }

    public String getOutLoanId() {
        return outLoanId;
    }

    public void setOutLoanId(String outLoanId) {
        this.outLoanId = outLoanId;
    }

    public String getOutCreditId() {
        return outCreditId;
    }

    public void setOutCreditId(String outCreditId) {
        this.outCreditId = outCreditId;
    }

    public String getOutRepayId() {
        return outRepayId;
    }

    public void setOutRepayId(String outRepayId) {
        this.outRepayId = outRepayId;
    }

    public String getRepayCardId() {
        return repayCardId;
    }

    public void setRepayCardId(String repayCardId) {
        this.repayCardId = repayCardId;
    }

    public String getPeriods() {
        return periods;
    }

    public void setPeriods(String periods) {
        this.periods = periods;
    }

    public WhetherState getNeedSmsCode() {
        return needSmsCode;
    }

    public void setNeedSmsCode(WhetherState needSmsCode) {
        this.needSmsCode = needSmsCode;
    }

    public BigDecimal getConsultationFeeWaiver() {
        return consultationFeeWaiver;
    }

    public void setConsultationFeeWaiver(BigDecimal consultationFeeWaiver) {
        this.consultationFeeWaiver = consultationFeeWaiver;
    }

    public BigDecimal getPenaltyInterestWaiver() {
        return penaltyInterestWaiver;
    }

    public void setPenaltyInterestWaiver(BigDecimal penaltyInterestWaiver) {
        this.penaltyInterestWaiver = penaltyInterestWaiver;
    }
}
