package com.maguo.loan.cash.flow.entrance.ppd.dto.req;

import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

import java.time.LocalDate;

/**
 * <AUTHOR>
 */
public class RepayTrialRequest {

    /**
     * 放款请求流水号
     */
    @NotBlank(message = "放款流水号不能为空")
    private String loanReqNo;
    /**
     * 请求方代码
     */
    @NotBlank(message = "请求方代码不能为空")
    private String sourceCode;
    /**
     * 还款类型
     */
    @NotBlank(message = "还款类型不能为空")
    private String repayType;
    /**
     * 还款日期,格式yyyyMMdd
     */
    @NotNull(message = "还款日期不能为空")
    @JsonFormat(pattern = "yyyyMMdd")
    private LocalDate repayDate;
    /**
     * 还款期次
     */
    @NotNull(message = "还款期次不能为空")
    private Integer repayTerm;

    public String getLoanReqNo() {
        return loanReqNo;
    }

    public void setLoanReqNo(String loanReqNo) {
        this.loanReqNo = loanReqNo;
    }

    public String getSourceCode() {
        return sourceCode;
    }

    public void setSourceCode(String sourceCode) {
        this.sourceCode = sourceCode;
    }

    public String getRepayType() {
        return repayType;
    }

    public void setRepayType(String repayType) {
        this.repayType = repayType;
    }

    public LocalDate getRepayDate() {
        return repayDate;
    }

    public void setRepayDate(LocalDate repayDate) {
        this.repayDate = repayDate;
    }

    public Integer getRepayTerm() {
        return repayTerm;
    }

    public void setRepayTerm(Integer repayTerm) {
        this.repayTerm = repayTerm;
    }
}
