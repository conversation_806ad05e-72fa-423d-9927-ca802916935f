package com.maguo.loan.cash.flow.util;

import org.apache.commons.net.util.Base64;
import javax.crypto.Cipher;
import javax.crypto.KeyGenerator;
import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.nio.charset.Charset;
import java.security.*;
import java.security.interfaces.RSAPrivateKey;
import java.security.interfaces.RSAPublicKey;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.UUID;

/**
 * @ClassName EncryptionUtil
 * @Description 加解密工具类，公钥加密，私钥解密
 */
public class EncryptionUtil {
    public static final Charset DEFAULT_CHARSET = Charset.forName("UTF-8");
    private static final int AES_KEY_SIZE = 128;
    private static final int RSA_KEY_SIZE = 1024;
    private static final int RSA_ENCRYPT_BLOCK_SIZE = 117;
    private static final int RSA_DECRYPT_BLOCK_SIZE = 128;
    private static final int HEX_STRING_LENGTH = 2;
    private static final int SUBSTRING_INDEX = 6;
    private static final int BYTE_MASK = 0xff;
    private static final int HEX_RADIX = 16;

    /**
     * 报文加密（公钥加密）
     * @param requestMessage 明文
     * @return String[] 返回数组，数组下表0为加密信息 数组下表1为加密密钥
     * @throws Exception
     */
    public static final String[] encrypt(byte[] requestMessage, String pubKey) throws Exception {
        String[] byteReturn = new String[HEX_STRING_LENGTH];
        String randomKey = getRandomKey();
        byte[] encodeMessageByte = aesEncrypt(hexStringToByte(randomKey), requestMessage);
        String eMessage = byteToHexString(encodeMessageByte);
        String signature = rsapubKeyEnc(randomKey, pubKey);
        byteReturn[0] = eMessage;
        byteReturn[1] = signature;
        return byteReturn;
    }

    /**
     * RSA私钥加密
     *
     * @param str
     *            加密字符串
     * @param publicKey
     *            公钥
     * @return 铭文
     * @throws Exception
     *             解密过程中的异常信息
     */
    public static String encrypt(String str, String publicKey) throws Exception {
        //分段加密
        byte[] decoded = Base64.decodeBase64(publicKey);
        RSAPublicKey pubKey = (RSAPublicKey) KeyFactory.getInstance("RSA").generatePublic(new X509EncodedKeySpec(decoded));
        Cipher cipher = Cipher.getInstance("RSA");
        cipher.init(Cipher.ENCRYPT_MODE, pubKey);
        byte[] bytes = str.getBytes();
        int inputLen = bytes.length;
        int offLen = 0; //偏移量
        int i = 0;
        ByteArrayOutputStream bops = new ByteArrayOutputStream();
        while (inputLen - offLen > 0) {
            byte[] cache;
            if (inputLen - offLen > RSA_ENCRYPT_BLOCK_SIZE) {
                cache = cipher.doFinal(bytes, offLen, RSA_ENCRYPT_BLOCK_SIZE);
            } else {
                cache = cipher.doFinal(bytes, offLen, inputLen - offLen);
            }
            bops.write(cache);
            i++;
            offLen = RSA_ENCRYPT_BLOCK_SIZE * i;
        }
        bops.close();
        byte[] encryptedData = bops.toByteArray();
        String encodeToString = Base64.encodeBase64String(encryptedData);
        return encodeToString;
    }

    /**
     * 报文解密（私钥解密）
     * @param message   密文
     * @param signature 密钥
     * @param privKey 私钥
     * @return String 解密后的明文
     * @throws Exception
     */
    public static final String decrypt(String message, String signature, String privKey) throws Exception {
        String dKey = rsaprivKeyDec(signature, privKey);
        byte[] stringByte = aesDecrypt(hexStringToByte(dKey), hexStringToByte(message));
        return new String(stringByte, DEFAULT_CHARSET);
    }


    /**
     * RSA私钥解密
     *
     * @param str
     *            加密字符串
     * @param privateKey
     *            私钥
     * @return 铭文
     * @throws Exception
     *             解密过程中的异常信息
     */
    public static String decrypt(String str, String privateKey) throws Exception {
        //分段解密
        byte[] decoded = Base64.decodeBase64(privateKey);
        RSAPrivateKey priKey = (RSAPrivateKey) KeyFactory.getInstance("RSA").generatePrivate(new PKCS8EncodedKeySpec(decoded));
        Cipher cipher = Cipher.getInstance("RSA");
        cipher.init(Cipher.DECRYPT_MODE, priKey);
        byte[] bytes = Base64.decodeBase64(str);
        int inputLen = bytes.length;
        int offLen = 0;
        int i = 0;
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        while (inputLen - offLen > 0) {
            byte[] cache;
            if (inputLen - offLen > RSA_DECRYPT_BLOCK_SIZE) {
                cache = cipher.doFinal(bytes, offLen, RSA_DECRYPT_BLOCK_SIZE);
            } else {
                cache = cipher.doFinal(bytes, offLen, inputLen - offLen);
            }
            byteArrayOutputStream.write(cache);
            i++;
            offLen = RSA_DECRYPT_BLOCK_SIZE * i;
        }
        byteArrayOutputStream.close();
        byte[] byteArray = byteArrayOutputStream.toByteArray();
        return new String(byteArray);
    }



    /**
     * 生成随机字符串和秘钥
     */
    private static String getRandomKey() throws NoSuchAlgorithmException {
        return getRandomKeyByStr(UUID.randomUUID().toString(), AES_KEY_SIZE);
    }

    /**
     * aes加密
     */
    private static byte[] aesEncrypt(byte[] key, byte[] src) throws Exception {
        SecretKeySpec skeySpec = new SecretKeySpec(key, "AES");
        Cipher cipher = Cipher.getInstance("AES");
        cipher.init(Cipher.ENCRYPT_MODE, skeySpec);
        return cipher.doFinal(src);
    }

    /**
     * 使用指定的字符串生成秘钥
     */
    private static String getRandomKeyByStr(String secretKey, int size) throws NoSuchAlgorithmException {
        KeyGenerator kg = KeyGenerator.getInstance("AES");
        kg.init(size, new SecureRandom(secretKey.getBytes()));
        SecretKey sk = kg.generateKey();
        byte[] b = sk.getEncoded();
        return byteToHexString(b);
    }

    /**
     * rsa加密
     * @param content 待机加密明文
     * @param pubKey  RSA公钥
     * @return 密文
     */
    public static String rsapubKeyEnc(String content, String pubKey) throws Exception {
        InputStream is = null;
        try {
            KeyFactory keyf = KeyFactory.getInstance("RSA");
            //获取公钥
            is = new ByteArrayInputStream(pubKey.getBytes("utf-8"));
            byte[] pubbytes = new byte[pubKey.length()];
            is.read(pubbytes);
            X509EncodedKeySpec pubX509 = new X509EncodedKeySpec(Base64.decodeBase64(pubbytes));
            PublicKey publicKey = keyf.generatePublic(pubX509);
            //公钥加密
            Cipher cipher = Cipher.getInstance("RSA/ECB/PKCS1Padding");
            cipher.init(Cipher.ENCRYPT_MODE, publicKey);
            byte[] cipherText = cipher.doFinal(content.getBytes());
            return Base64.encodeBase64String(cipherText);
        } catch (Exception e) {
            throw new RuntimeException(e);
        } finally {
            if (is != null) {
                is.close();
            }
        }
    }

    /**
     * aes解密
     */
    public static byte[] aesDecrypt(byte[] key, byte[] encrypted) throws Exception {
        SecretKeySpec skeySpec = new SecretKeySpec(key, "AES");
        Cipher cipher = Cipher.getInstance("AES");
        cipher.init(Cipher.DECRYPT_MODE, skeySpec);
        byte[] decrypted = cipher.doFinal(encrypted);
        return decrypted;
    }

    /**
     * rsa解密
     * @param ciphertext 经BASE84编码过的待解密密文
     * @param privKey    RSA私钥
     * @return utf-8编码的明文
     */
    public static String rsaprivKeyDec(String ciphertext, String privKey) throws Exception {
        KeyFactory keyf = KeyFactory.getInstance("RSA");
        //获取私钥
        InputStream key = new ByteArrayInputStream(privKey.getBytes("utf-8"));
        byte[] pribytes = new byte[privKey.length()];
        key.read(pribytes);
        byte[] buffer = Base64.decodeBase64(pribytes);
        PKCS8EncodedKeySpec priPKCS8 = new PKCS8EncodedKeySpec(buffer);
        PrivateKey privateKey = keyf.generatePrivate(priPKCS8);
        Cipher cipher = Cipher.getInstance("RSA/ECB/PKCS1Padding");
        cipher.init(Cipher.DECRYPT_MODE, privateKey);
        byte[] text = Base64.decodeBase64(ciphertext);
        byte[] content = cipher.doFinal(text);
        return new String(content, "UTF-8");
    }

    /**
     * 二进制byte[]转十六进制string
     */
    private static String byteToHexString(byte[] bytes) {
        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < bytes.length; i++) {
            String strHex = Integer.toHexString(bytes[i]);
            if (strHex.length() > HEX_STRING_LENGTH + 1) {
                sb.append(strHex.substring(SUBSTRING_INDEX));
            } else {
                if (strHex.length() < HEX_STRING_LENGTH) {
                    sb.append("0" + strHex);
                } else {
                    sb.append(strHex);
                }
            }
        }
        return sb.toString();
    }

    /**
     * 十六进制string转二进制byte[]
     */
    private static byte[] hexStringToByte(String s) {
        byte[] baKeyword = new byte[s.length() / HEX_STRING_LENGTH];
        for (int i = 0; i < baKeyword.length; i++) {
            try {
                baKeyword[i] = (byte) (BYTE_MASK & Integer.parseInt(s.substring(i * HEX_STRING_LENGTH, i * HEX_STRING_LENGTH + HEX_STRING_LENGTH), HEX_RADIX));
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return baKeyword;
    }

    /**
     * 生成秘钥
     * @throws NoSuchAlgorithmException
     */
    public static String[] createRSAKeyPairs() throws Exception {
        KeyPairGenerator generator = KeyPairGenerator.getInstance("RSA");
        generator.initialize(RSA_KEY_SIZE, new SecureRandom());
        KeyPair pair = generator.generateKeyPair();
        PublicKey pubKey = pair.getPublic();
        PrivateKey privKey = pair.getPrivate();
        byte[] pubk = pubKey.getEncoded();
        byte[] privk = privKey.getEncoded();
        String strpk = new String(Base64.encodeBase64(pubk));
        String strprivk = new String(Base64.encodeBase64(privk));
        String[] keyPairs = new String[2];
        keyPairs[0] = strpk;
        keyPairs[1] = strprivk;
        return keyPairs;
    }



}
