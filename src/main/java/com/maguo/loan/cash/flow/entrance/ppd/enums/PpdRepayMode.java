package com.maguo.loan.cash.flow.entrance.ppd.enums;

/**
 * <AUTHOR>
 * @Description 还款模式
 * @Date 2025/1/9 18:55
 **/
public enum PpdRepayMode {
    ONLINE("01", "线上还款"),
    OFFLINE("02", "线下还款"),
    CLAIM("03", "代偿"),
    REPURCHASE("04", "回购");

    private final String code;
    private final String desc;

    PpdRepayMode(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
