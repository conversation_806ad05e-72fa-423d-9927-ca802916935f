package com.jinghang.capital.core.banks.cybk.enums;

/**
 * @作者 Mr.sandman
 * @时间 2025/07/03 11:42
 */
public enum CYBKVendorEnum {

  YC("YC", "云从"),
  KS("KS", "旷视"),
  YT("YT", "依图"),
  YOT("YOT", "优图"),
  OTH("OTH", "其他");

  private final String code;
  private final String desc;

  CYBKVendorEnum( String code, String desc ) {
    this.code = code;
    this.desc = desc;
  }

  public String getCode() {
    return code;
  }

  public String getDesc() {
    return desc;
  }

  /**
   * 根据 code 获取对应的枚举实例
   *
   * @param code 代码
   * @return 对应的枚举实例，如果未找到则返回 null
   */
  public static CYBKVendorEnum getByCode( String code ) {
    for ( CYBKVendorEnum vendor : CYBKVendorEnum.values()) {
      if (vendor.getCode().equals(code)) {
        return vendor;
      }
    }
    return null;
  }

}
