package com.jinghang.capital.core.banks.cybk.dto.limit;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.jinghang.capital.core.banks.cybk.dto.CYBKBaseRequest;
import com.jinghang.capital.core.banks.cybk.enums.CYBKTradeCode;
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class CYBKLimitQueryRequest extends CYBKBaseRequest {
    @JsonIgnore
    private static final CYBKTradeCode TRADE_CODE = CYBKTradeCode.LIMIT_QUERY;
    /**
     * 商户码值
     */
    private String merchantNo;
    /**
     * 门店码值
     */
    private String storeCode;
    /**
     * 终端类型
     */
    private String terminalType;
    /**
     * 外部申请流水号
     */
    private String outApplSeq;
    /**
     * 长银授信流水号
     */
    private String applCde;

    @Override
    public CYBKTradeCode getTradeCode() {
        return TRADE_CODE;
    }

    public String getMerchantNo() {
        return merchantNo;
    }

    public void setMerchantNo(String merchantNo) {
        this.merchantNo = merchantNo;
    }

    public String getStoreCode() {
        return storeCode;
    }

    public void setStoreCode(String storeCode) {
        this.storeCode = storeCode;
    }

    public String getTerminalType() {
        return terminalType;
    }

    public void setTerminalType(String terminalType) {
        this.terminalType = terminalType;
    }

    public String getOutApplSeq() {
        return outApplSeq;
    }

    public void setOutApplSeq(String outApplSeq) {
        this.outApplSeq = outApplSeq;
    }

    public String getApplCde() {
        return applCde;
    }

    public void setApplCde(String applCde) {
        this.applCde = applCde;
    }
}
