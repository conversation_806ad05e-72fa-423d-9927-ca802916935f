package com.jinghang.capital.core.banks.cybk.dto.loan;

import java.math.BigDecimal;

public class CYBKLoanRiskInfo {

    /**
     * 渠道类型
     */
    private String channelType;
    /**
     * 新老客户标识
     */
    private String isNewCustomer;
    /**
     * 申请金额
     */
    private BigDecimal businessCreditAmount;
    /**
     * 申请期限
     */
    private String applyPeriod;

    /**
     * 授信利率
     */
    private String creditRate;
    /**
     * 对客综合年化利率
     */
    private String businessRiskPrice;
    /**
     * 客户风险评分
     */
    private String creditScore;
    /**
     * 客户风险等级
     */
    private String creditLevel;


    public String getChannelType() {
        return channelType;
    }

    public void setChannelType(String channelType) {
        this.channelType = channelType;
    }

    public String getIsNewCustomer() {
        return isNewCustomer;
    }

    public void setIsNewCustomer(String isNewCustomer) {
        this.isNewCustomer = isNewCustomer;
    }

    public BigDecimal getBusinessCreditAmount() {
        return businessCreditAmount;
    }

    public void setBusinessCreditAmount(BigDecimal businessCreditAmount) {
        this.businessCreditAmount = businessCreditAmount;
    }

    public String getApplyPeriod() {
        return applyPeriod;
    }

    public void setApplyPeriod(String applyPeriod) {
        this.applyPeriod = applyPeriod;
    }

    public String getCreditRate() {
        return creditRate;
    }

    public void setCreditRate(String creditRate) {
        this.creditRate = creditRate;
    }

    public String getBusinessRiskPrice() {
        return businessRiskPrice;
    }

    public void setBusinessRiskPrice(String businessRiskPrice) {
        this.businessRiskPrice = businessRiskPrice;
    }

    public String getCreditScore() {
        return creditScore;
    }

    public void setCreditScore(String creditScore) {
        this.creditScore = creditScore;
    }

    public String getCreditLevel() {
        return creditLevel;
    }

    public void setCreditLevel(String creditLevel) {
        this.creditLevel = creditLevel;
    }
}
