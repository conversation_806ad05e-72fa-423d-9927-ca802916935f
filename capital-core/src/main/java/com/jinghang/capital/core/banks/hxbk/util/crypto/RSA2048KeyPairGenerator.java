package com.jinghang.capital.core.banks.hxbk.util.crypto;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.security.KeyPair;
import java.security.KeyPairGenerator;
import java.security.NoSuchAlgorithmException;
import java.util.Base64;

/**
 * RSA 2048位密钥对生成器
 * 用于生成HXBK加解密所需的RSA密钥对
 *
 * @Author: Lior
 * @CreateTime: 2025/7/8 20:00
 */
public class RSA2048KeyPairGenerator {

    private static final Logger logger = LoggerFactory.getLogger(RSA2048KeyPairGenerator.class);

    /**
     * 生成RSA 2048位密钥对
     *
     * @return 密钥对信息数组，[0]为公钥，[1]为私钥
     */
    public static String[] generateKeyPair() {
        try {
            KeyPairGenerator keyPairGenerator = KeyPairGenerator.getInstance("RSA");
            keyPairGenerator.initialize(2048);
            
            KeyPair keyPair = keyPairGenerator.generateKeyPair();
            
            String publicKey = Base64.getEncoder().encodeToString(keyPair.getPublic().getEncoded());
            String privateKey = Base64.getEncoder().encodeToString(keyPair.getPrivate().getEncoded());
            
            logger.info("RSA 2048位密钥对生成成功");
            logger.debug("公钥长度: {}, 私钥长度: {}", publicKey.length(), privateKey.length());
            
            return new String[]{publicKey, privateKey};
        } catch (NoSuchAlgorithmException e) {
            logger.error("生成RSA密钥对失败", e);
            throw new RuntimeException("生成RSA密钥对失败", e);
        }
    }

    /**
     * 主方法，用于生成密钥对并打印
     *
     * @param args 命令行参数
     */
    public static void main(String[] args) {
        try {
            logger.info("开始生成RSA 2048位密钥对...");
            
            String[] keyPair = generateKeyPair();
            String publicKey = keyPair[0];
            String privateKey = keyPair[1];
            
            System.out.println("=== RSA 2048位密钥对生成结果 ===");
            System.out.println("公钥: " + publicKey);
            System.out.println();
            System.out.println("私钥: " + privateKey);
            System.out.println();
            System.out.println("=== 密钥对生成完成 ===");
            
            logger.info("RSA 2048位密钥对生成并输出完成");
        } catch (Exception e) {
            logger.error("密钥对生成过程中发生异常", e);
            System.err.println("密钥对生成失败: " + e.getMessage());
        }
    }
}
