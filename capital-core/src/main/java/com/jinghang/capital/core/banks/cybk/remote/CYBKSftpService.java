package com.jinghang.capital.core.banks.cybk.remote;


import com.jinghang.capital.core.banks.cybk.config.CYBKSftpConfig;
import com.jinghang.common.io.IoUtil;
import com.jinghang.common.sftp.DestMapping;
import com.jinghang.common.sftp.Sftp;
import com.jinghang.common.sftp.exception.SftpException;
import com.jinghang.common.util.SftpUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.nio.file.Files;
import java.nio.file.Path;

@Service
public class CYBKSftpService {
    private static final Logger logger = LoggerFactory.getLogger(CYBKSftpService.class);

    private final CYBKSftpConfig config;


    @Autowired
    public CYBKSftpService(CYBKSftpConfig config) {
        this.config = config;
    }

    public void upload(String sftpFile, String localFile) {
        DestMapping uploadMapping = new DestMapping(sftpFile, localFile);
        Sftp sftp = getSftp();
        try {
            sftp.upload(uploadMapping);
        } catch (SftpException e) {
            logger.error("upload CYBK error, mapping: {}", uploadMapping, e);
            throw new RuntimeException(e);
        }
    }

    public Path downloadOssFile(Path tempDir, String filePath, InputStream is) {
        Path targetPath = tempDir.resolve(filePath);
        try (OutputStream os = Files.newOutputStream(targetPath)) {
            IoUtil.copy(is, os);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        return targetPath;
    }

    Path createTempDir() {
        Path path;
        try {
            path = Files.createTempDirectory("CYBK");
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        return path;
    }

    private Sftp getSftp() {
        return SftpUtil.use(config.getCybkSftpUsername(), config.getCybkSftpPassword(),
                config.getCybkSftpHost(), config.getCybkSftpPort());
    }
}
