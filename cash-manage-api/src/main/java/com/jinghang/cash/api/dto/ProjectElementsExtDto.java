package com.jinghang.cash.api.dto;

import com.jinghang.cash.api.enums.*;
import lombok.Data;

import java.io.Serializable;

/**
 * 项目要素扩展实体
 *
 * @Author: Lior
 * @CreateTime: 2025/8/20 10:56
 */
@Data
public class ProjectElementsExtDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private String id;

    /**
     * 上级要素ID
     */
    private String parentId;

    /**
     * 关联的项目唯一编码
     */
    private String projectCode;

    private String projectName;

    /**
     * 年利率基数(天) (如 360或365)
     */
    private String interestDaysBasis;

    /**
     * 是否支持线下跨日还款
     */
    private ActiveInactive allowCrossDayRepay;

    /**
     * 风控模型渠道
     */
    private RiskModelChannel riskModelChannel;

    /**
     * 放款支付渠道
     */
    private LoanPayChannel loanPaymentChannel;

    /**
     * 扣款绑卡渠道
     */
    private DeductChannel deductionBindCardChannel;

    /**
     * 扣款商户号
     */
    private String deductionMerchantCode;

    /**
     * 签章渠道
     */
    private SignChannel signChannel;

    /**
     * 逾期短信发送方
     */
    private String overdueSmsSender;

    /**
     * 短信渠道
     */
    private SmsChannel smsChannel;

    /**
     * 逾期宽限期类型 (SQ:首期, MQ:每期)
     */
    private GracePeriodType gracePeriodType;

    /**
     * 逾期宽限期(天)
     */
    private String gracePeriodDays;

    /**
     * 节假日是否顺延
     */
    private ActiveInactive holidayPostpone;

    /**
     * 征信查询方
     */
    private String creditQueryParty;

    /**
     * 征信上报方
     */
    private String creditReportSender;

    /**
     * 催收方
     */
    private String collectionParty;

    /**
     * 是否推送催收数据
     */
    private ActiveInactive pushCollectionData;

    /**
     * 是否推送客诉数据
     */
    private ActiveInactive pushKsData;

    /**
     * 是否支持催收减免
     */
    private ActiveInactive allowCollectionWaiver;

    /**
     *  是否续借
     */
    private ActiveInactive renew;
    /**
     * 对客减免
     */
    private ActiveInactive customerReduction;

    /**
     * 对资减免
     */
    private ActiveInactive fundingReduction;


}
