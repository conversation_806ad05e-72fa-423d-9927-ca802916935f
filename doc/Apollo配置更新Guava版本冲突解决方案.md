# Apollo配置更新Guava版本冲突解决方案

## 🚨 问题描述

在更新Apollo配置后，系统出现以下错误：

```
java.lang.NoClassDefFoundError: com/google/common/collect/Sets$2$1
        at com.google.common.collect.Sets.intersection(Sets.java:792)
        at com.ctrip.framework.apollo.internals.AbstractConfig.calcPropertyChanges(AbstractConfig.java:583)
```

## 🔍 问题分析

### 根本原因
这是一个典型的**Guava版本兼容性问题**：

1. **版本冲突**：Apollo客户端2.2.0版本依赖的Guava版本与Spring Boot 3.2.1引入的Guava版本不兼容
2. **内部类变化**：`Sets$2$1`是Guava内部类，不同版本的Guava有不同的内部实现
3. **传递依赖**：Spring Boot 3.x和Spring Cloud 2023.0.0引入了更新版本的Guava，与Apollo期望的版本冲突

### 技术栈版本信息
- **Spring Boot**: 3.2.1
- **Spring Cloud**: 2023.0.0
- **Apollo Client**: 2.2.0
- **Java**: 17

## 🛠️ 解决方案

### 方案1：显式声明Guava依赖版本（已实施）

在主pom.xml中添加Guava依赖管理，强制使用兼容版本：

#### 1. 添加版本属性
```xml
<properties>
    <!-- 其他属性... -->
    <!-- 解决Apollo与Spring Boot 3.x的Guava版本冲突 -->
    <guava.version>32.1.3-jre</guava.version>
</properties>
```

#### 2. 添加依赖管理
```xml
<dependencyManagement>
    <dependencies>
        <!-- 其他依赖... -->
        <!-- 解决Apollo与Spring Boot 3.x的Guava版本冲突 -->
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
            <version>${guava.version}</version>
        </dependency>
    </dependencies>
</dependencyManagement>
```

### 方案2：排除冲突依赖（备选方案）

如果方案1不生效，可以在Apollo依赖中排除Guava：

```xml
<dependency>
    <groupId>com.ctrip.framework.apollo</groupId>
    <artifactId>apollo-client-config-data</artifactId>
    <version>${apollo.version}</version>
    <exclusions>
        <exclusion>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
        </exclusion>
    </exclusions>
</dependency>
```

### 方案3：升级Apollo版本（长期方案）

考虑升级到更新的Apollo版本，以获得更好的Spring Boot 3.x兼容性：

```xml
<apollo.version>2.3.0</apollo.version>
```

## 🔧 验证步骤

### 1. 清理和重新构建
```bash
mvn clean install -DskipTests
```

### 2. 检查依赖树
```bash
mvn dependency:tree | grep guava
```

### 3. 启动应用验证
启动应用并观察Apollo配置加载是否正常，无错误日志。

## 📋 版本兼容性矩阵

| Spring Boot | Spring Cloud | Apollo Client | 推荐Guava版本 |
|-------------|--------------|---------------|---------------|
| 3.2.1       | 2023.0.0     | 2.2.0         | 32.1.3-jre    |
| 3.1.x       | 2022.0.x     | 2.1.0         | 31.1-jre      |
| 2.7.x       | 2021.0.x     | 2.0.1         | 30.1.1-jre   |

## 🚀 最佳实践

### 1. 依赖管理
- 在父pom中统一管理所有依赖版本
- 使用`<dependencyManagement>`避免版本冲突
- 定期检查和更新依赖版本

### 2. 版本升级策略
- 优先升级框架版本以获得更好兼容性
- 测试环境充分验证后再部署生产
- 保持技术栈版本的一致性

### 3. 监控和排查
- 使用`mvn dependency:tree`检查依赖冲突
- 启用详细日志排查启动问题
- 建立版本兼容性测试流程

## 📝 注意事项

1. **测试覆盖**：修改后需要全面测试Apollo配置功能
2. **环境一致性**：确保所有环境使用相同的依赖版本
3. **回滚准备**：保留原始配置以便必要时回滚
4. **文档更新**：更新部署文档中的依赖版本信息

## 🔗 相关链接

- [Apollo官方文档](https://www.apolloconfig.com/)
- [Spring Boot依赖管理](https://docs.spring.io/spring-boot/docs/current/reference/html/dependency-versions.html)
- [Guava版本发布说明](https://github.com/google/guava/releases)

---
**更新时间**: 2025-01-17  
**解决状态**: ✅ 已解决  
**验证状态**: ⏳ 待验证
