1. 本系统获取影像件：List<LoanFile> imageFileList = getCommonService().getLoanFileRepository().findByCreditId(creditId);
从imageFileList中的获取每一项的fileType，只获取ID_HEAD、ID_NATION、ID_FACE这三个值，分别是身份证人头面、身份证国徽面、人脸
2. 获取到这三张影像件后，分成身份证和人脸两类，上传到蚂蚁的sftp服务器中
3. 蚂蚁的路径配置获取：src/main/java/com/jinghang/capital/batch/domain/hxbk/HXBKConfig.java
3. 身份证的上传规则如下：
   用途	身份证影印件备份			
   业务规则	包括的合同文件类型（contractType）： idcard（身份证影印件）			
   提供方	天枢平台			
   提供时间	T+5m提供			
   使用方	合作机构			
   类型	jpg、png、jpeg			
   所在目录规则	"/download/contract/idcard/${yyyyMMdd}/IDCARD-xxxxxx.zip
   /download/contract/idcard/${yyyyMMdd}/IDCARD-xxxxxx.zip.txt
   yyyyMMdd为当前日期
   xxxxxx为申请流水号号"			
   "每天的身份证影印件文件放在当天日期的目录中
   文件分批进行上传
   每次上传一个zip包，包含当前批次的合同文件
   每个zip包对应一个txt文件，为身份证影印件索引文件，可通过此文件内容找到业务号与身份证影印文件的关系"			
   目录示例	"/download/contract/idcard/20180724/IDCARD-xxxxxx.zip
   /download/contract/idcard/20180724/IDCARD-xxxxxx.zip.txt"			
   获取规则	"先获取txt索引文件，再根据索引文件获取对应zip包里的合同文件
   如果文件上传中，会以 .tmp 结尾，上传完成之后修改为正确的名字，先上传zip文件，再上传该zip对应的txt索引文件"			
   zip文件名称规则	IDCARD-xxxxxx.zip  说明：xxxxxx为批次号			
   文件名称规则	"未加密版本
   ${applySerialNo}_${idCardNo}_x.jpg
   ${applySerialNo}_${idCardNo}_y.jpg
   加密版本
   en_${applySerialNo}_${idCardNo}_x.jpg
   en_${applySerialNo}_${idCardNo}_y.jpg
   说明：applySerialNo为授信申请单号，idCardNo为用户身份证号, _x结尾为身份证影印件个人信息面，_y结尾为身份证影印件国徽面"			
   索引文件规则	"按行展示，每行逗号隔开，字段说明：
   业务类型,子业务类型,业务单号,文件类型,文件名称"			
   "TIANSHU,PLATFORM1,201606120000000135A,IDCARDX,201606120000000135A_232321198812101221_x.jpg
   TIANSHU,PLATFORM1,201606120000000135A,IDCARDY,201606120000000135A_232321198812101221_y.jpg
   TIANSHU,PLATFORM1,201606120000000135A,EN_IDCARDX,en_201606120000000135A_232321198812101221_x.jpg
   TIANSHU,PLATFORM1,201606120000000135A,EN_IDCARDY,en_201606120000000135A_232321198812101221_y.jpg
   表示天枢，平台一期，授信申请单号为201606120000000135A，文件类型为身份证个人信息面、国徽面分别在对应在zip里的文件名为：201606120000000135A_232321198812101221_x.jpg、201606120000000135A_232321198812101221_y.jpg"			
   字符编码	UTF-8			
4. 人脸（生物活体照片）的上传规则如下：
   用途	生物活体照片备份			
   业务规则	包括的合同文件类型（contractType）： photo（生物活体照片）			
   提供方	天枢平台			
   提供时间	T+5m提供			
   使用方	合作机构			
   类型	jpg、png、jpeg			
   所在目录规则	"/download/contract/photo/${yyyyMMdd}/PHOTO-xxxxxx.zip
   /download/contract/photo/${yyyyMMdd}/PHOTO-xxxxxx.zip.txt
   yyyyMMdd为当前日期
   xxxxxx为内部的批次号"			
   "每天的生物活体照片文件放在当天日期的目录中
   文件分批进行上传
   每次上传一个zip包，包含当前批次的合同文件
   每个zip包对应一个txt文件，为生物活体照片索引文件，可通过此文件内容找到业务号与生物活体照片文件的关系"			
   目录示例	"/download/contract/photo/20180724/PHOTO-xxxxxx.zip
   /download/contract/photo/20180724/PHOTO-xxxxxx.zip.txt"			
   获取规则	"先获取txt索引文件，再根据索引文件获取对应zip包里的合同文件
   如果文件上传中，会以 .tmp 结尾，上传完成之后修改为正确的名字，先上传zip文件，再上传该zip对应的txt索引文件"			
   zip文件名称规则	PHOTO-xxxxxx.zip  说明：xxxxxx为批次号			
   文件名称规则	"未加密版本：
   ${applyNo}.jpg
   加密版本：
   en_${applyNo}.jpg
   说明：applyNo为授信申请单号"			
   索引文件规则	"按行展示，每行逗号隔开，字段说明：
   业务类型,子业务类型,业务单号,文件类型,文件名称"			
   "TIANSHU,PLATFORM1,201606120000000135A,PHOTO,201606120000000135A.jpg
   TIANSHU,PLATFORM1,201606120000000135A,EN_PHOTO,en_201606120000000135A.jpg
   表示天枢，平台一期，授信申请单号为201606120000000135A。"			
   字符编码	UTF-8			
5. 加密规则
   本平台所有文件，压缩包内均需使用加密算法对文件内容进行加密。
   在加解密过程中会使用到128位AES/ECB/PKCS5Padding对称加密算法。

实现概述
0.项目启动时，由蚂蚁侧提供机构编号(platformNo)，机构保存后用于后续AES秘钥Seed创建
1.蚂蚁侧将文件加密存储入压缩包，并按照文档中命名方式对文件、压缩包进行命名
2.机构根据压缩包命名解析申请流水号(serialNo)，并根据秘钥Seed生成规则得到秘钥，对压缩包内文件进行解密

秘钥Seed生成规则：以下划线拼接机构编号与申请流水号后，对字符串进行倒序。demo：revert(${platformNo}_${serialNo})

具体操作：
蚂蚁侧
文件加密
1、根据revert(${platformNo}_${serialNo})，生成AESKeySeed
2、基于AESKeySeed，生成AES秘钥
3、基于AES秘钥，加密文件内容，并以对应文件命名文件
4、基于加密完成文件，根据压缩包命名规则，命名压缩包。
解密验签
1、根据压缩包名称，提取serialNo
2、根据revert(${platformNo}_${serialNo})，生成AESKeySeed
3、基于AESKeySeed，生成AES秘钥
4、基于AES秘钥，解密文件内容
5、按需保存、流转文件
工具类已经有了，src/main/java/com/jinghang/capital/batch/util/AESUtil.encryptFile