# HXBK影像件检查功能使用说明

## 📋 概述

本文档说明如何使用新增的 `checkImageFilesExist` 方法来检查蚂蚁SFTP上是否存在指定授信申请单号的身份证和人脸影像件。

## 🎯 功能说明

### 方法签名

```java
public boolean checkImageFilesExist(String applySerialNo)
```

### 参数说明

- **applySerialNo**: 授信申请单号，用于查找对应的影像件

### 返回值

- **true**: 身份证影像件和人脸影像件都存在
- **false**: 身份证影像件或人脸影像件不存在，或者检查过程中发生异常

## 🔍 检查逻辑

### 检查范围

该方法会检查以下两类影像件：

1. **身份证影像件**: 位于 `${idCardDir}/${yyyyMMdd}/IDCARD-*.zip`
2. **人脸影像件**: 位于 `${photoDir}/${yyyyMMdd}/PHOTO-*.zip`

其中：
- `idCardDir`: 从配置 `mayi.sftp.download.idcard` 获取
- `photoDir`: 从配置 `mayi.sftp.download.photo` 获取
- `yyyyMMdd`: 当前日期，格式为年月日

### 检查条件

- 只有当**身份证影像件**和**人脸影像件**都存在时，方法才返回 `true`
- 任何一类影像件不存在，都会返回 `false`
- 发生异常时也会返回 `false`

## 💻 使用示例

### 基本使用

```java
@Autowired
private HXBKImageFileService hxbkImageFileService;

public void checkCreditImageFiles(String creditId) {
    // 检查影像件是否存在
    boolean exists = hxbkImageFileService.checkImageFilesExist(creditId);
    
    if (exists) {
        logger.info("授信申请 {} 的影像件已存在", creditId);
        // 继续后续业务逻辑
    } else {
        logger.warn("授信申请 {} 的影像件不完整", creditId);
        // 处理影像件缺失的情况
    }
}
```

### 在授信流程中使用

```java
public void processCreditApplication(String creditId) {
    // 1. 检查影像件是否已上传
    if (!hxbkImageFileService.checkImageFilesExist(creditId)) {
        throw new BizException("影像件未完整上传，请先上传身份证和人脸影像件");
    }
    
    // 2. 继续授信申请流程
    // ...
}
```

### 批量检查

```java
public Map<String, Boolean> batchCheckImageFiles(List<String> creditIds) {
    Map<String, Boolean> results = new HashMap<>();
    
    for (String creditId : creditIds) {
        boolean exists = hxbkImageFileService.checkImageFilesExist(creditId);
        results.put(creditId, exists);
    }
    
    return results;
}
```

## 📊 日志输出

### 正常情况

```
INFO  - 开始检查HXBK影像件是否存在, applySerialNo: 202507110000001
INFO  - 检查身份证影像件目录: /download/contract/idcard/20250711
INFO  - 身份证影像件检查结果, applySerialNo: 202507110000001, 存在: true
INFO  - 检查人脸影像件目录: /download/contract/photo/20250711
INFO  - 人脸影像件检查结果, applySerialNo: 202507110000001, 存在: true
INFO  - HXBK影像件检查完成, applySerialNo: 202507110000001, 身份证存在: true, 人脸存在: true, 全部存在: true
```

### 文件不存在

```
INFO  - 开始检查HXBK影像件是否存在, applySerialNo: 202507110000002
INFO  - 检查身份证影像件目录: /download/contract/idcard/20250711
INFO  - 未找到身份证影像件, applySerialNo: 202507110000002, 目录: /download/contract/idcard/20250711
INFO  - 检查人脸影像件目录: /download/contract/photo/20250711
INFO  - 人脸影像件检查结果, applySerialNo: 202507110000002, 存在: true
INFO  - HXBK影像件检查完成, applySerialNo: 202507110000002, 身份证存在: false, 人脸存在: true, 全部存在: false
```

### 异常情况

```
ERROR - 检查HXBK影像件是否存在失败, applySerialNo: 202507110000003
com.jinghang.common.sftp.exception.SftpException: SFTP连接失败
    at ...
```

## ⚠️ 注意事项

### 1. 时间依赖性

- 该方法基于**当前日期**查找影像件目录
- 如果影像件是在其他日期上传的，可能无法找到
- 建议在影像件上传的同一天进行检查

### 2. 实现细节

- 使用 `sftp.custom()` 方法访问底层 `ChannelSftp` 对象
- 通过数组变量在lambda表达式中传递结果值
- 异常情况下自动返回 `false`，确保方法的健壮性

### 3. 网络依赖性

- 该方法需要连接到蚂蚁SFTP服务器
- 网络异常时会返回 `false`
- 建议在调用前确保网络连接正常

### 4. 权限要求

- 需要有读取SFTP目录的权限
- 确保配置的SFTP用户名和密码正确

### 5. 性能考虑

- 该方法会建立SFTP连接并查询文件
- 频繁调用可能影响性能
- 建议根据业务需要合理控制调用频率

## 🔧 配置要求

确保以下SFTP配置正确：

```yaml
mayi:
  sftp:
    username: ${MAYI_SFTP_USERNAME}
    password: ${MAYI_SFTP_PASSWORD}
    host: ${MAYI_SFTP_HOST}
    port: ${MAYI_SFTP_PORT}
    download:
      idcard: /download/contract/idcard
      photo: /download/contract/photo
```

## 🧪 测试建议

### 单元测试

```java
@Test
public void testCheckImageFilesExist() {
    // 准备测试数据
    String applySerialNo = "TEST202507110001";
    
    // 执行检查
    boolean result = hxbkImageFileService.checkImageFilesExist(applySerialNo);
    
    // 验证结果
    assertTrue(result);
}
```

### 集成测试

1. 准备测试环境的SFTP服务器
2. 上传测试影像件
3. 调用检查方法验证结果
4. 清理测试数据

---

**文档维护者**: 开发团队  
**最后更新**: 2025-07-11  
**文档版本**: v1.0
