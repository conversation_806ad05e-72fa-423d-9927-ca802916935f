# HXBK加解密流程分析

## 1. 整体流程概述

根据文档描述，HXBK与蚂蚁天枢系统之间的通信采用双重加密机制：
- **RSA 2048位非对称加密**：用于加密/解密SecretKey
- **AES 128位对称加密**：用于加密/解密业务数据
- **SHA256WithRSA签名**：用于数据完整性验证

## 2. SecretKey生成机制

### 2.1 SecretKey是什么？
SecretKey是一个**随机生成的UUID字符串**，用作AES加密的密钥。

### 2.2 SecretKey生成位置
在代码中，SecretKey在以下两个地方生成：

**发送请求时（ParamUtils.buildRequest方法）：**
```java
String secretKey = UUID.randomUUID().toString();  // 第68行
```

**发送响应时（ParamUtils.buildResponse方法）：**
```java
String secretKey = UUID.randomUUID().toString();  // 第119行
```

### 2.3 SecretKey的作用流程
1. **生成**：每次通信都会生成一个新的随机UUID作为SecretKey
2. **AES加密业务数据**：使用SecretKey对业务JSON数据进行AES加密
3. **RSA加密SecretKey**：使用对方的RSA公钥对SecretKey进行加密
4. **传输**：将加密后的SecretKey和加密后的业务数据一起发送
5. **解密**：接收方用自己的RSA私钥解密SecretKey，再用SecretKey解密业务数据

## 3. 密钥交换和配置

### 3.1 需要配置的密钥
根据配置文件，需要配置以下密钥：

```properties
# 合作方（我方）的RSA私钥
hxbk.callback.partner.privateKey=xxxxx

# 蚂蚁侧的RSA公钥  
hxbk.callback.ant.publicKey=xxxxx
```

### 3.2 密钥生成和交换流程
1. **我方生成RSA密钥对**：public1（我方公钥）, private1（我方私钥）
2. **蚂蚁侧生成RSA密钥对**：public2（蚂蚁公钥）, private2（蚂蚁私钥）
3. **密钥交换**：
   - 我方获得：蚂蚁公钥（public2）
   - 蚂蚁获得：我方公钥（public1）

## 4. 蚂蚁需要给我们什么参数

### 4.1 回调请求参数结构
蚂蚁会发送以下格式的回调请求：

```json
{
  "appid": "dubhe",
  "method": "具体的业务方法名",
  "version": "1.0", 
  "timestamp": "时间戳",
  "signType": "SHA256WithRSA",
  "encrypt": "1",
  "secretKey": "RSA加密后的SecretKey",
  "data": "AES加密后的业务数据",
  "requestId": "请求ID",
  "sign": "SHA256WithRSA签名"
}
```

### 4.2 各参数说明
- **method**: 业务方法名，用于路由到不同的处理逻辑
- **secretKey**: 用蚂蚁公钥加密的AES密钥
- **data**: 用SecretKey进行AES加密的业务数据
- **sign**: 对除sign外所有参数按字母排序后的签名

## 5. 我们需要给蚂蚁什么参数

### 5.1 响应参数结构
我们需要返回以下格式的响应：

```json
{
  "code": "000000",
  "msg": "请求成功", 
  "data": "AES加密后的业务响应数据",
  "encrypt": "1",
  "secretKey": "RSA加密后的SecretKey",
  "responseId": "响应ID",
  "sign": "SHA256WithRSA签名"
}
```

### 5.2 响应构建流程
1. **生成新的SecretKey**（UUID）
2. **AES加密业务响应数据**
3. **用蚂蚁公钥RSA加密SecretKey**
4. **构建响应参数**（除sign外）
5. **用我方私钥对参数进行SHA256WithRSA签名**
6. **返回完整响应**

## 6. 代码中的处理流程

### 6.1 接收蚂蚁回调的处理流程（HXBKCallbackApiController）
```java
// 1. 解析请求JSON
Map<String, Object> requestJson = JsonUtil.convertToObject(requestBody, Map.class);

// 2. 验签（使用蚂蚁公钥验证蚂蚁的签名）
boolean verified = RSAUtils.verifyData(signature, toVerifyStr, cryptoUtil.getAntPublicKey());

// 3. 解密SecretKey（使用我方私钥解密）
String secretKey = RSAUtils.decryptSecretKeyWithRSA(encryptedSecretKey, cryptoUtil.getPartnerPrivateKey());

// 4. 解密业务数据（使用解密后的SecretKey）
String bizDataStr = AESUtils.decryptDataWithAES(encryptedData, secretKey);

// 5. 业务处理
MethodResponse responseData = callbackService.processCallback(method, requestId, bizDataStr);

// 6. 构建加密响应
return cryptoUtil.buildSuccessResponse(responseData);
```

### 6.2 发送请求给蚂蚁的处理流程（ParamUtils.buildRequest）
```java
// 1. 生成随机SecretKey
String secretKey = UUID.randomUUID().toString();

// 2. 用蚂蚁公钥加密SecretKey
String encryptedSecretKey = RSAUtils.encryptSecretKeyWithRSA(secretKey, publicKey);

// 3. 用SecretKey加密业务数据
String encryptedData = AESUtils.encryptDataWithAES(bizDataStr, secretKey);

// 4. 构建请求参数并签名
String signature = RSAUtils.signData(toSignStr, privateKey);
```

## 7. 关键点总结

1. **SecretKey是临时的**：每次通信都会生成新的UUID作为SecretKey
2. **双向加密**：请求和响应都需要加密SecretKey和业务数据
3. **双向签名**：请求和响应都需要用各自的私钥签名，对方用公钥验签
4. **参数排序**：签名前需要将参数按字母顺序排序
5. **配置要求**：需要配置蚂蚁公钥和我方私钥

## 8. 详细参数说明

### 8.1 蚂蚁回调请求参数详解

| 参数名 | 类型 | 必填 | 说明 | 在代码中的使用位置 |
|--------|------|------|------|-------------------|
| appid | String | 是 | 固定值"dubhe" | 用于验签，不直接使用 |
| method | String | 是 | 业务方法名 | 用于路由到具体业务处理 |
| version | String | 是 | 版本号，固定"1.0" | 用于验签，不直接使用 |
| timestamp | String | 是 | 时间戳 | 用于验签，不直接使用 |
| signType | String | 是 | 签名类型，固定"SHA256WithRSA" | 用于验签，不直接使用 |
| encrypt | String | 是 | 是否加密，固定"1" | 用于验签，不直接使用 |
| secretKey | String | 是 | RSA加密后的AES密钥 | 用我方私钥解密得到AES密钥 |
| data | String | 是 | AES加密后的业务数据 | 用解密后的secretKey解密得到业务数据 |
| requestId | String | 是 | 请求唯一标识 | 用于日志记录和业务处理 |
| sign | String | 是 | SHA256WithRSA签名 | 用蚂蚁公钥验证请求完整性 |

### 8.2 我方响应参数详解

| 参数名 | 类型 | 必填 | 说明 | 在代码中的生成位置 |
|--------|------|------|------|-------------------|
| code | String | 是 | 响应码，成功为"000000" | HXBKCryptoUtil.buildSuccessResponse |
| msg | String | 是 | 响应消息 | HXBKCryptoUtil.buildSuccessResponse |
| data | String | 是 | AES加密后的业务响应数据 | ParamUtils.buildResponse |
| encrypt | String | 是 | 是否加密，固定"1" | ParamUtils.buildResponse |
| secretKey | String | 是 | RSA加密后的AES密钥 | ParamUtils.buildResponse |
| responseId | String | 是 | 响应唯一标识 | ParamUtils.buildResponse |
| sign | String | 是 | SHA256WithRSA签名 | ParamUtils.buildResponse |

### 8.3 业务数据结构
业务数据（data字段解密后）的具体结构取决于method参数：

- **method不同，业务数据结构不同**
- **解密后的业务数据是JSON格式字符串**
- **具体的业务数据结构需要根据蚂蚁提供的接口文档确定**

### 8.4 签名验证流程
```java
// 1. 提取除sign外的所有参数
TreeMap<String, String> paramsToVerify = new TreeMap<>();
paramsToVerify.put("appid", "dubhe");
paramsToVerify.put("method", method);
// ... 其他参数

// 2. 按字母顺序排序并拼接
String toVerifyStr = paramsToVerify.entrySet().stream()
    .map(e -> e.getKey() + "=" + e.getValue())
    .collect(Collectors.joining("&"));

// 3. 使用蚂蚁公钥验证签名
boolean verified = RSAUtils.verifyData(signature, toVerifyStr, antPublicKey);
```

## 9. 配置示例

```properties
# 我方RSA私钥（PKCS8格式，Base64编码）
hxbk.callback.partner.privateKey=MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQC...

# 蚂蚁RSA公钥（X509格式，Base64编码）
hxbk.callback.ant.publicKey=MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA...
```

## 10. 常见问题和注意事项

### 10.1 SecretKey相关
- **每次通信都会生成新的SecretKey**，不是固定的
- **SecretKey是UUID格式的字符串**，不是二进制数据
- **SecretKey用于AES加密，本身用RSA加密传输**

### 10.2 签名相关
- **签名前必须按字母顺序排序参数**
- **sign参数本身不参与签名计算**
- **签名使用SHA256WithRSA算法**

### 10.3 加密相关
- **业务数据使用AES/ECB/PKCS5Padding加密**
- **SecretKey使用RSA/ECB/PKCS1Padding加密**
- **所有加密结果都进行Base64编码**
