package com.jinghang.capital.core.banks.hxbk.util.crypto;

import com.jinghang.common.util.JsonUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;
import java.util.TreeMap;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * HXBK加解密工具使用示例和测试
 * 演示完整的加解密、签名验签流程
 *
 * @Author: Lior
 * @CreateTime: 2025/7/8 20:00
 */
public class CryptoDemo {

    private static final Logger logger = LoggerFactory.getLogger(CryptoDemo.class);

    // 示例密钥对（实际使用时应从配置文件读取）
    private static final String PARTNER_PRIVATE_KEY = "使用 RSA2048KeyPairGenerator 生成的合作方私钥";
    private static final String PARTNER_PUBLIC_KEY = "使用 RSA2048KeyPairGenerator 生成的合作方公钥";
    private static final String ANT_PUBLIC_KEY = "蚂蚁侧提供的公钥";
    private static final String ANT_PRIVATE_KEY = "蚂蚁侧私钥（仅用于测试）";

    /**
     * 演示接受蚂蚁侧请求的处理流程
     *
     * @param antRequest 蚂蚁侧请求JSON字符串
     * @return 处理结果响应JSON字符串
     */
    public static String acceptAntRequest(String antRequest) {
        logger.info("=== 开始处理蚂蚁侧请求 ===");
        
        try {
            @SuppressWarnings("unchecked")
            Map<String, Object> bodyJson = JsonUtil.convertToObject(antRequest, Map.class);
            
            // 第1步：解析入参
            TreeMap<String, String> paramsToVerifyMap = ParamUtils.parseParamFromRequest(bodyJson);
            String toVerifyStr = paramsToVerifyMap.entrySet().stream()
                    .map(e -> e.getKey() + "=" + e.getValue())
                    .collect(Collectors.joining("&"));
            logger.info("构建待验签字符串完成，长度: {}", toVerifyStr.length());

            // 第2步：验签
            String signature = (String) bodyJson.get("sign");
            boolean verified = RSAUtils.verifyData(signature, toVerifyStr, ANT_PUBLIC_KEY);
            logger.info("请求验签结果: {}", verified);

            if (verified) {
                // 第3步：使用己方私钥解密SecretKey
                String secretKey = RSAUtils.decryptSecretKeyWithRSA(
                        (String) bodyJson.get("secretKey"), PARTNER_PRIVATE_KEY);
                logger.info("SecretKey解密成功");

                // 第4步：解密业务数据
                String bizData = AESUtils.decryptDataWithAES((String) bodyJson.get("data"), secretKey);
                logger.info("业务数据解密成功，数据长度: {}", bizData.length());

                // 第5步：处理业务逻辑
                String bizResult = doOwnBusiness(bizData);

                // 第6步：构建响应
                String responseStr = buildSuccessResponse(bizResult);
                logger.info("=== 蚂蚁侧请求处理完成 ===");
                return responseStr;
            } else {
                logger.error("请求验签失败");
                return buildFailResponse(VERIFY_FAIL_CODE, "验签失败");
            }
        } catch (Exception e) {
            logger.error("处理蚂蚁侧请求异常", e);
            return buildFailResponse("500000", "处理请求异常: " + e.getMessage());
        }
    }

    /**
     * 演示回调蚂蚁侧服务的流程
     *
     * @param method 接口方法名
     * @param bizData 业务数据
     * @return 回调结果
     */
    public static Map<String, Object> callbackAntService(String method, Map<String, Object> bizData) {
        logger.info("=== 开始回调蚂蚁侧服务 ===");
        
        try {
            // 第1步：构建请求参数
            String bizDataStr = JsonUtil.toJsonString(bizData);
            String requestStr = ParamUtils.buildRequest(method, bizDataStr, ANT_PUBLIC_KEY, PARTNER_PRIVATE_KEY);
            logger.info("构建回调请求完成，请求数据长度: {}", requestStr.length());

            // 第2步：模拟发送请求并获得响应（实际应调用HTTP接口）
            String antResponseStr = simulateAntResponse();
            logger.info("模拟接收蚂蚁侧响应完成");

            // 第3步：验签并解密蚂蚁侧的响应
            Map<String, Object> resultJson = ParamUtils.verifySignAndDecryptResponse(
                    antResponseStr, ANT_PUBLIC_KEY, PARTNER_PRIVATE_KEY);
            logger.info("=== 蚂蚁侧服务回调完成 ===");
            return resultJson;
        } catch (Exception e) {
            logger.error("回调蚂蚁侧服务异常", e);
            Map<String, Object> errorResult = new TreeMap<>();
            errorResult.put("code", "500000");
            errorResult.put("msg", "回调异常: " + e.getMessage());
            return errorResult;
        }
    }

    /**
     * 业务逻辑处理（示例）
     *
     * @param bizData 业务数据
     * @return 处理结果
     */
    private static String doOwnBusiness(String bizData) {
        logger.info("=== 执行业务逻辑处理 ===");
        logger.debug("接收到业务数据: {}", bizData);
        
        // 这里可以添加具体的业务处理逻辑
        Map<String, Object> result = new TreeMap<>();
        result.put("enableApply", "Y");
        result.put("processTime", System.currentTimeMillis());
        result.put("message", "业务处理成功");
        
        return JsonUtil.toJsonString(result);
    }

    /**
     * 构建成功响应
     *
     * @param bizResult 业务结果
     * @return 响应JSON字符串
     */
    private static String buildSuccessResponse(String bizResult) {
        TreeMap<String, String> response = new TreeMap<>();
        response.put("code", "000000");
        response.put("msg", "请求成功");
        
        return ParamUtils.buildResponse(response, bizResult, ANT_PUBLIC_KEY, PARTNER_PRIVATE_KEY);
    }

    /**
     * 构建失败响应
     *
     * @param code 错误码
     * @param msg  错误信息
     * @return 响应JSON字符串
     */
    private static String buildFailResponse(String code, String msg) {
        TreeMap<String, String> response = new TreeMap<>();
        response.put("code", code);
        response.put("msg", msg);
        response.put("responseId", UUID.randomUUID().toString());
        
        return JsonUtil.toJsonString(response);
    }

    /**
     * 模拟蚂蚁侧响应（用于测试）
     *
     * @return 模拟响应JSON字符串
     */
    private static String simulateAntResponse() {
        Map<String, Object> mockBizData = new TreeMap<>();
        mockBizData.put("result", "success");
        mockBizData.put("timestamp", System.currentTimeMillis());
        
        TreeMap<String, String> response = new TreeMap<>();
        response.put("code", "000000");
        response.put("msg", "请求成功");
        
        return ParamUtils.buildResponse(response, JsonUtil.toJsonString(mockBizData), 
                PARTNER_PUBLIC_KEY, ANT_PRIVATE_KEY);
    }

    /**
     * 测试完整的加解密流程
     */
    public static void testFullCryptoFlow() {
        logger.info("=== 开始测试完整加解密流程 ===");
        
        try {
            // 1. 生成测试密钥对
            logger.info("1. 生成测试密钥对");
            String[] partnerKeyPair = RSA2048KeyPairGenerator.generateKeyPair();
            String[] antKeyPair = RSA2048KeyPairGenerator.generateKeyPair();
            
            // 2. 测试AES加解密
            logger.info("2. 测试AES加解密");
            String testData = "这是测试数据";
            String secretKey = UUID.randomUUID().toString();
            String encrypted = AESUtils.encryptDataWithAES(testData, secretKey);
            String decrypted = AESUtils.decryptDataWithAES(encrypted, secretKey);
            logger.info("AES测试结果 - 原文: {}, 解密: {}, 匹配: {}", testData, decrypted, testData.equals(decrypted));
            
            // 3. 测试RSA加解密
            logger.info("3. 测试RSA加解密");
            String encryptedKey = RSAUtils.encryptSecretKeyWithRSA(secretKey, partnerKeyPair[0]);
            String decryptedKey = RSAUtils.decryptSecretKeyWithRSA(encryptedKey, partnerKeyPair[1]);
            logger.info("RSA测试结果 - 原文: {}, 解密: {}, 匹配: {}", 
                    secretKey.substring(0, 8) + "...", 
                    decryptedKey.substring(0, 8) + "...", 
                    secretKey.equals(decryptedKey));
            
            // 4. 测试数字签名
            logger.info("4. 测试数字签名");
            String signData = "待签名数据";
            String signature = RSAUtils.signData(signData, partnerKeyPair[1]);
            boolean verified = RSAUtils.verifyData(signature, signData, partnerKeyPair[0]);
            logger.info("签名测试结果 - 验签: {}", verified);
            
            logger.info("=== 完整加解密流程测试完成 ===");
        } catch (Exception e) {
            logger.error("测试过程中发生异常", e);
        }
    }

    private static final String VERIFY_FAIL_CODE = "100000";

    /**
     * 主方法，用于运行测试
     *
     * @param args 命令行参数
     */
    public static void main(String[] args) {
        logger.info("=== HXBK加解密工具演示开始 ===");
        
        // 运行完整流程测试
        testFullCryptoFlow();
        
        // 可以在这里添加更多测试用例
        
        logger.info("=== HXBK加解密工具演示结束 ===");
    }
}
