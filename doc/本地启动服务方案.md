# JH-Loan-Cash 系统本地启动服务方案

## 📋 系统架构概述

JH-Loan-Cash 是一个完整的微服务借贷系统，包含以下主要服务：

### 🎯 核心业务服务
| 服务名 | 端口 | 服务标识 | Apollo App ID | 说明 |
|--------|------|----------|---------------|------|
| **cash-flow** | 8001 | cash-flow | loan-cash-flow-paipai | 主业务流程服务 |
| **capital-core** | 8001 | capital-core-service | capital-core | 资金核心服务 |
| **capital-batch** | 8100 | fin-batch | capital-batch | 批处理任务服务 |
| **cash-manage** | - | cash-manage | cash-flow-manage | 后台管理服务 |
| **sing-service** | 8081 | sing-service | sing-service | 签章服务 |

## 🏗️ 中间件使用分析

### 公用服务 (团队共享，不需要本地部署)
| 中间件 | 用途 | 使用场景 | 风险评估 |
|--------|------|----------|----------|
| **MySQL** | 关系型数据库 | 业务数据存储 | ✅ 可共享 |
| **Redis** | 缓存/分布式锁 | 缓存、分布式锁 | ✅ 可共享 |
| **Apollo** | 配置中心 | 动态配置管理 | ✅ 可共享 |
| **Eureka** | 服务注册发现 | 微服务通信 | ⚠️ **需要隔离** |

### 本地启动服务 (需要在本地启动)
| 服务  | 必需性 | 说明   |
|------|------|------|
| **RabbitMQ** | 必需 | 消息队列 |

### 可选服务 (按业务需要)
| 中间件 | 用途 | 何时需要 | 部署方式 |
|--------|------|----------|----------|
| **XXL-Job** | 分布式任务调度 | capital-batch需要执行定时任务时 | 公用或本地 |
| **SFTP服务器** | 文件传输 | 业务功能涉及文件上传下载时 | 公用 |

## 🚨 关键问题分析

### RabbitMQ 隔离问题
**问题**: 系统大量使用MQ，包含以下关键队列：
- `risk.apply` - 风控申请
- `credit.apply` - 授信申请
- `loan.apply` - 放款申请
- `repay.*` - 还款相关
- `sms.send` - 短信发送

**风险**: 如果多个开发者连接同一个RabbitMQ，会导致：
- 消息被其他开发者消费
- 业务流程被打断
- 调试困难

### Eureka 隔离分析
**服务调用关系**:
- `cash-flow` → `capital-core-service`
- `cash-flow` → `sing-service`
- `cash-manage` → `cash-flow`

**隔离可行性**: Eureka可以公用，因为：
- 服务名称不同，天然隔离
- 本地端口不同，不会冲突
- OpenFeign通过服务名+负载均衡调用，只会路由到对应服务

## 🚀 本地启动方案

### 中间件部署策略
| 中间件 | 部署方式 | 隔离方式 | 说明 |
|--------|----------|----------|------|
| **MySQL** | 可公用 | 数据库名隔离 | 如: capital_core_dev_张三 |
| **Redis** | 可公用 | key前缀隔离 | 如: dev-张三: |
| **Apollo** | 公用 | 应用ID已隔离 | 无需修改 |
| **RabbitMQ** | 本地部署 | 完全隔离 | 避免消息冲突 |
| **Eureka** | 公用 | Zone隔离 | 通过zone实现隔离 |

### 本地启动服务
- **flow** (8001) - 必需
- **capital-core** (8001) - 必需

## 🎯 启动步骤

### 基础命令：
```shell
# 检查服务状态
net start | findstr RabbitMQ

# 如果未运行，启动服务
net start RabbitMQ

# 停止服务
net stop RabbitMQ

# 启动MQ服务
rabbitmqctl start_app

# 获取状态
rabbitmqctl status
```

### 第一步：安装启动本地RabbitMQ
安装RabbitMQ：https://cloud.tencent.com/developer/article/2303666

管理地址：http://127.0.0.1:15672  
默认账号：guest  
默认密码：guest

### 第二步：配置RabbitMQ
```shell
# 创建用户
rabbitmqctl add_user dev dev

# 添加权限
rabbitmqctl set_user_tags dev administrator

# 添加虚拟主机 (可选，根据项目决定)
rabbitmqctl add_vhost cash-flow-paipai-FAT

# 设置权限
rabbitmqctl set_permissions -p / dev ".*" ".*" ".*"
rabbitmqctl set_permissions -p cash-flow-paipai-FAT dev ".*" ".*" ".*"

# 查看虚拟主机
rabbitmqctl list_vhosts
```

### 第三步：在IDEA中覆盖Apollo的RabbitMQ配置
在IDEA的Configrations配置中，添加 Override configuration properties，或者添加VM arguments。
```properties
spring.rabbitmq.address=localhost:5672
spring.rabbitmq.username=dev
spring.rabbitmq.password=dev
```

### 问题：
```
1. Socket closed
不能用默认账户guest，需要创建用户

2. Error: unable to connect to node ***: nodedown diagnostics
Erlang cookies不匹配
从C:\Windows\system32\config\systemprofile目录下找到erlang.cookie文件，然后复制到用户目录C:\Users\<USER>