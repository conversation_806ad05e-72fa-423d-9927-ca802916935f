# Flow订单入库时机分析

## 📋 概述

本文档详细分析了jh-loan-cash-flow系统中订单(Order)的入库时机和完整流程，包括预订单(PreOrder)和正式订单(Order)的创建、更新过程。

## 🔄 完整流程时序

```mermaid
sequenceDiagram
    participant User as 用户
    participant API as ApprovalOperateService
    participant PreDB as pre_order表
    participant Risk as 风控系统
    participant Listener as RiskResultEventListener
    participant OrderDB as order表
    participant Service as OrderService

    User->>API: 提交申请(ApprovalReqDTO)
    API->>PreDB: 保存预订单(INIT状态)
    API->>Risk: 提交风控申请
    API->>PreDB: 更新状态为AUDITING
    
    Risk-->>Listener: 风控结果事件(RiskResultEvent)
    alt 风控通过
        Listener->>OrderDB: 创建正式订单(AUDIT_PASS状态)
        Listener->>PreDB: 更新预订单为AUDIT_PASS
    else 风控拒绝
        Listener->>PreDB: 更新预订单为AUDIT_REJECT
    end
    
    User->>Service: 用户下单申请
    Service->>OrderDB: 更新订单状态(CREDITING)
    Service->>OrderDB: 订单路由处理
```

## 📊 入库时机详细分析

### 1. 预订单阶段 (PreOrder)

#### 1.1 首次入库 - 用户申请时

**触发位置**: `ApprovalOperateService.approval()`

**入库时机**: 用户通过各个流量渠道提交申请时

```java
// 保存预订单
PreOrder preOrder = CommonApiCovert.INSTANCE.toPreOrder(reqDTO);
preOrder.setFlowChannel(ThreadLocalUtil.getFlowChannel());
preOrder = preOrderRepository.save(preOrder);  // 第一次入库：预订单
```

**状态**: `PreOrderState.INIT`

#### 1.2 状态更新 - 提交风控后

```java
//更新预订单为审核中
preOrder.setPreOrderState(PreOrderState.AUDITING);
preOrderRepository.save(preOrder);  // 更新入库
```

**状态**: `PreOrderState.AUDITING`

### 2. 正式订单阶段 (Order)

#### 2.1 订单创建 - 风控通过后

**触发位置**: `RiskResultEventListener.onApplicationEvent()`

**入库时机**: 风控审批通过后，通过事件监听器创建

```java
@EventListener(RiskResultEvent.class)
public void onApplicationEvent(RiskResultEvent riskResultEvent) {
    // 风控结果处理
    if (AuditState.PASS == approveResult) {
        // 生成订单
        Order order = createOrder(record);  // 创建正式订单
        // 更新预订单状态
        updatePreOrderPass(record);
    }
}
```

**具体创建过程**:

```java
private Order createNormalOrder(UserRiskRecord record) {
    //初始化订单
    order = new Order();
    order.setUserId(userId);
    order.setFlowChannel(flowChannel);
    order.setOpenId(preOrder.getOpenId());
    order.setOuterOrderId(preOrder.getOrderNo());
    order.setRiskId(riskId);
    order.setApplyTime(LocalDateTime.now());
    order.setApplyAmount(preOrder.getApplyAmount());
    order.setOrderState(OrderState.AUDIT_PASS);  // 设置为风控通过状态
    order.setApplyPeriods(preOrder.getApplyPeriods());
    order.setAmountType(preOrder.getAmountType());
    order.setRenewedFlag(WhetherState.N);
    order.setApproveAmount(preOrder.getApplyAmount());
    // ... 设置其他属性
    
    return orderRepository.save(order);  // 第二次入库：正式订单
}
```

**状态**: `OrderState.AUDIT_PASS`

#### 2.2 订单状态更新 - 业务流程中

##### 2.2.1 用户下单申请

**触发位置**: `OrderService.apply()`

```java
public void apply(String orderId, WhetherState rightsMarking) {
    Order order = orderRepository.findById(orderId).orElseThrow();
    
    // 设置提交状态和权益标记
    order.setOrderSubmitState(WhetherState.Y);
    order.setRightsMarking(rightsMarking);
    
    if (Arrays.asList(riskConfig.getRiskLoanFlowChannel().split(",")).contains(order.getFlowChannel().name())) {
        //放款风控
        order.setOrderState(OrderState.CREDITING);
        order = orderRepository.saveAndFlush(order);  // 更新入库
    } else {
        order = orderRepository.saveAndFlush(order);  // 更新入库
        orderRoute(order);
    }
}
```

##### 2.2.2 订单路由

**触发位置**: `OrderService.orderRoute()`

```java
public void orderRoute(Order order) {
    String orderId = order.getId();
    String flowName = order.getFlowChannel().name();
    
    //挂起订单
    if (Arrays.asList(flowSuspended.split(",")).contains(flowName)) {
        order.setOrderState(OrderState.SUSPENDED);
        orderRepository.save(order);  // 状态更新入库
    } else {
        // 置授信中
        order.setOrderState(OrderState.CREDITING);
        orderRepository.save(order);  // 状态更新入库
        // 开始路由
        mqService.submitCreditRouteApply(orderId);
    }
}
```

## 📈 订单状态流转图

```mermaid
stateDiagram-v2
    [*] --> PreOrder_INIT: 用户申请
    PreOrder_INIT --> PreOrder_AUDITING: 提交风控
    PreOrder_AUDITING --> PreOrder_AUDIT_PASS: 风控通过
    PreOrder_AUDITING --> PreOrder_AUDIT_REJECT: 风控拒绝
    
    PreOrder_AUDIT_PASS --> Order_AUDIT_PASS: 创建正式订单
    Order_AUDIT_PASS --> Order_CREDITING: 用户下单/订单路由
    Order_AUDIT_PASS --> Order_SUSPENDED: 订单挂起
    
    Order_CREDITING --> Order_CREDIT_PASS: 授信通过
    Order_CREDITING --> Order_CREDIT_FAIL: 授信失败
    
    Order_CREDIT_PASS --> Order_LOANING: 开始放款
    Order_LOANING --> Order_LOAN_PASS: 放款成功
    Order_LOANING --> Order_LOAN_FAIL: 放款失败
    
    Order_LOAN_PASS --> Order_CLEAR: 结清
    Order_AUDIT_PASS --> Order_LOAN_CANCEL: 放款取消
    Order_SUSPENDED --> Order_LOAN_CANCEL: 放款取消
```

## 📋 入库时机汇总表

| 序号 | 阶段 | 时机 | 表 | 状态 | 触发条件 | 关键方法 |
|------|------|------|-----|------|----------|----------|
| 1 | 预订单创建 | 用户申请 | `pre_order` | `INIT` | 用户提交申请 | `ApprovalOperateService.approval()` |
| 2 | 预订单更新 | 风控提交 | `pre_order` | `AUDITING` | 提交风控审批 | `ApprovalOperateService.approval()` |
| 3 | 正式订单创建 | 风控通过 | `order` | `AUDIT_PASS` | 风控结果事件 | `RiskResultEventListener.createNormalOrder()` |
| 4 | 预订单更新 | 风控通过 | `pre_order` | `AUDIT_PASS` | 风控通过 | `RiskResultEventListener.updatePreOrderPass()` |
| 5 | 订单状态更新 | 用户下单 | `order` | `CREDITING` | 用户确认下单 | `OrderService.apply()` |
| 6 | 订单状态更新 | 订单路由 | `order` | `CREDITING/SUSPENDED` | 订单路由处理 | `OrderService.orderRoute()` |
| 7 | 订单状态更新 | 授信结果 | `order` | `CREDIT_PASS/CREDIT_FAIL` | 授信完成 | `CreditResultEventListener` |
| 8 | 订单状态更新 | 放款结果 | `order` | `LOAN_PASS/LOAN_FAIL` | 放款完成 | 放款相关服务 |

## 🔍 关键代码位置

### 核心入库方法

1. **预订单入库**: 
   - 文件: `src/main/java/com/maguo/loan/cash/flow/entrance/common/service/ApprovalOperateService.java`
   - 方法: `approval(ApprovalReqDTO reqDTO)`

2. **正式订单入库**: 
   - 文件: `src/main/java/com/maguo/loan/cash/flow/service/event/listener/RiskResultEventListener.java`
   - 方法: `createNormalOrder(UserRiskRecord record)`

3. **订单状态更新**: 
   - 文件: `src/main/java/com/maguo/loan/cash/flow/service/OrderService.java`
   - 方法: `apply()`, `orderRoute()`, `update()` 等

### Repository接口

- **PreOrderRepository**: 预订单数据访问
- **OrderRepository**: 正式订单数据访问

## 🎯 重要说明

1. **双表设计**: 系统采用预订单(pre_order)和正式订单(order)双表设计，预订单用于风控前的数据存储，正式订单用于业务流程处理。

2. **事件驱动**: 正式订单的创建是通过风控结果事件(`RiskResultEvent`)驱动的，确保了风控通过后才创建正式订单。

3. **状态管理**: 订单在整个生命周期中会经历多次状态更新，每次状态变更都会触发数据库更新。

4. **并发控制**: 在关键操作中使用了分布式锁来防止重复提交和并发问题。

## 📝 总结

Flow的order入库是一个分阶段、事件驱动的过程：
- **第一阶段**: 用户申请时创建预订单
- **第二阶段**: 风控通过后创建正式订单  
- **第三阶段**: 业务流程中不断更新订单状态

这种设计确保了数据的完整性和业务流程的可控性，同时通过事件机制实现了系统的解耦。
