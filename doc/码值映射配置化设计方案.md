# 码值映射配置化设计方案

## 1. 背景分析

### 1.1 现状问题
- 系统分为流量、融担、资金三方，各方码值不统一
- 码值转换逻辑硬编码在枚举和转换器中，维护困难
- 新增渠道或修改码值映射需要修改代码并重新发布
- 缺乏统一的码值管理机制

### 1.2 目标
- 实现流量→融担、融担→资金的码值映射配置化
- 最小化代码改动，保持现有业务逻辑不变
- 支持动态配置，无需重启应用
- 提供统一的码值管理界面

## 2. 设计方案

### 2.1 总体架构

```
流量码值 → [映射配置表] → 融担码值 → [映射配置表] → 资金码值
```

### 2.2 数据库设计

#### 2.2.1 码值映射配置表 (code_value_mapping)

```sql
CREATE TABLE code_value_mapping (
    id VARCHAR(32) PRIMARY KEY COMMENT '主键ID',
    mapping_type VARCHAR(50) NOT NULL COMMENT '映射类型(FLOW_TO_GUARANTEE/GUARANTEE_TO_CAPITAL)',
    source_system VARCHAR(50) NOT NULL COMMENT '源系统(FLOW/GUARANTEE/CAPITAL)',
    target_system VARCHAR(50) NOT NULL COMMENT '目标系统(FLOW/GUARANTEE/CAPITAL)',
    source_channel VARCHAR(50) COMMENT '源渠道(如LVXIN,PPD,CYBK等)',
    target_channel VARCHAR(50) COMMENT '目标渠道',
    code_type VARCHAR(50) NOT NULL COMMENT '码值类型(EDUCATION,MARRIAGE,INCOME等)',
    source_code VARCHAR(100) NOT NULL COMMENT '源码值',
    source_name VARCHAR(200) COMMENT '源码值名称',
    target_code VARCHAR(100) NOT NULL COMMENT '目标码值',
    target_name VARCHAR(200) COMMENT '目标码值名称',
    priority INT DEFAULT 0 COMMENT '优先级(数字越大优先级越高)',
    enabled TINYINT(1) DEFAULT 1 COMMENT '是否启用(1-启用,0-禁用)',
    remark VARCHAR(500) COMMENT '备注',
    revision VARCHAR(32) COMMENT '版本号',
    created_by VARCHAR(50) COMMENT '创建人',
    created_time DATETIME COMMENT '创建时间',
    updated_by VARCHAR(50) COMMENT '更新人',
    updated_time DATETIME COMMENT '更新时间',

    UNIQUE KEY uk_mapping (mapping_type, source_system, target_system, source_channel, target_channel, code_type, source_code),
    INDEX idx_mapping_query (mapping_type, source_system, target_system, code_type, enabled),
    INDEX idx_channel_query (source_channel, target_channel, code_type, enabled)
) COMMENT='码值映射配置表';
```

#### 2.2.2 码值类型配置表 (code_type_config)

```sql
CREATE TABLE code_type_config (
    id VARCHAR(32) PRIMARY KEY COMMENT '主键ID',
    code_type VARCHAR(50) NOT NULL UNIQUE COMMENT '码值类型',
    code_type_name VARCHAR(100) NOT NULL COMMENT '码值类型名称',
    description VARCHAR(500) COMMENT '描述',
    enum_class_name VARCHAR(200) COMMENT '对应的枚举类名',
    enabled TINYINT(1) DEFAULT 1 COMMENT '是否启用',
    created_by VARCHAR(50) COMMENT '创建人',
    created_time DATETIME COMMENT '创建时间',
    updated_by VARCHAR(50) COMMENT '更新人',
    updated_time DATETIME COMMENT '更新时间'
) COMMENT='码值类型配置表';
```

### 2.3 核心组件设计

#### 2.3.1 码值映射服务 (CodeValueMappingService)

```java
@Service
public class CodeValueMappingService {

    /**
     * 流量到融担码值映射
     */
    public String mapFlowToGuarantee(String sourceChannel, String codeType, String sourceCode);

    /**
     * 融担到资金码值映射
     */
    public String mapGuaranteeToCapital(String sourceChannel, String targetChannel, String codeType, String sourceCode);

    /**
     * 批量映射
     */
    public Map<String, String> batchMapping(String mappingType, String sourceChannel, String targetChannel,
                                          String codeType, List<String> sourceCodes);

    /**
     * 刷新缓存
     */
    public void refreshCache();
}
```

#### 2.3.2 码值映射缓存管理器

```java
@Component
public class CodeMappingCacheManager {

    private final Map<String, String> mappingCache = new ConcurrentHashMap<>();

    /**
     * 构建缓存Key
     */
    private String buildCacheKey(String mappingType, String sourceChannel, String targetChannel,
                               String codeType, String sourceCode);

    /**
     * 加载映射配置到缓存
     */
    public void loadMappingToCache();

    /**
     * 获取映射值
     */
    public String getMappingValue(String cacheKey);
}
```

### 2.4 现有代码改造方案

#### 2.4.1 枚举转换器改造

以 `CommonApiEnumCovert` 为例：

```java
@Mapper
public interface CommonApiEnumCovert {

    CommonApiEnumCovert INSTANCE = Mappers.getMapper(CommonApiEnumCovert.class);

    // 保留原有方法作为兜底
    @ValueMappings({
        @ValueMapping(source = MappingConstants.ANY_REMAINING, target = MappingConstants.NULL)
    })
    FlowChannel toFlowChannel(String flowChannel);

    // 新增配置化映射方法
    default String mapCodeValue(String sourceChannel, String codeType, String sourceCode) {
        return CodeValueMappingService.getInstance().mapFlowToGuarantee(sourceChannel, codeType, sourceCode);
    }
}
```

#### 2.4.2 转换逻辑改造

在现有的转换方法中增加配置化映射逻辑：

```java
@Named("toEducation")
static Education toEducation(String str, String sourceChannel) {
    // 1. 先尝试配置化映射
    String mappedValue = CodeValueMappingService.getInstance()
        .mapFlowToGuarantee(sourceChannel, "EDUCATION", str);

    if (StringUtil.isNotBlank(mappedValue)) {
        try {
            return Education.valueOf(mappedValue);
        } catch (IllegalArgumentException e) {
            // 映射值无效，继续使用原逻辑
        }
    }

    // 2. 兜底使用原有逻辑
    if (StringUtil.isBlank(str)) {
        return null;
    }
    // ... 原有逻辑
}
```

### 2.5 实现步骤

#### 阶段一：基础设施搭建
1. 创建数据库表结构
2. 创建实体类和Repository
3. 实现码值映射服务和缓存管理器
4. 创建管理界面（可选）

#### 阶段二：现有代码改造
1. 识别需要改造的转换器和方法
2. 逐步改造转换逻辑，增加配置化映射
3. 保留原有逻辑作为兜底机制
4. 添加单元测试

#### 阶段三：配置数据迁移
1. 分析现有枚举映射关系
2. 将硬编码的映射关系迁移到配置表
3. 验证映射正确性

#### 阶段四：优化和监控
1. 添加映射性能监控
2. 优化缓存策略
3. 添加映射失败告警

## 3. 优势分析

### 3.1 最小改动原则
- 保留现有枚举和转换器结构
- 新增配置化映射作为优先选择
- 原有逻辑作为兜底机制，确保系统稳定性

### 3.2 扩展性强
- 支持任意渠道间的码值映射
- 支持新增码值类型
- 支持优先级和条件映射

### 3.3 运维友好
- 支持动态配置，无需重启
- 提供缓存机制，保证性能
- 支持批量操作和数据导入导出

## 4. 风险控制

### 4.1 兼容性保证
- 配置化映射失败时自动降级到原有逻辑
- 分阶段改造，确保每个阶段都可回滚
- 充分的单元测试和集成测试

### 4.2 性能保证
- 使用本地缓存减少数据库查询
- 支持缓存预热和定时刷新
- 监控映射性能，及时发现问题

### 4.3 数据一致性
- 配置变更需要审核流程
- 支持配置版本管理
- 提供配置回滚机制

## 5. 后续扩展

### 5.1 管理界面
- 码值映射配置管理
- 批量导入导出功能
- 映射关系可视化

### 5.2 高级功能
- 条件映射（基于其他字段值）
- 映射规则引擎
- 映射变更历史追踪

这个方案既保证了系统的稳定性，又实现了配置化的目标，是一个渐进式的改造方案。
