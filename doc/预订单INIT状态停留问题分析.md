# 预订单INIT状态停留问题分析

## 📋 问题概述

**预警信息**:
- 时间范围: 2025-08-01 09:30:00 ~ 2025-08-04 09:30:00
- 问题: cash-flow.preOrder_record不是最终态条数为:1
- 订单号: POR25080220032026823700734336379
- 当前状态: INIT

## 🔍 问题分析

### 1. 预订单状态流转正常流程

```mermaid
graph TD
    A[用户申请] --> B[创建预订单 INIT]
    B --> C[图片上传/OCR识别]
    C --> D[在途订单检查]
    D --> E[用户注册]
    E --> F[创建风控记录]
    F --> G[提交风控申请 MQ]
    G --> H[更新状态为 AUDITING]
    H --> I[风控处理]
    I --> J[AUDIT_PASS/AUDIT_REJECT]
```

### 2. 可能导致停留在INIT状态的原因

#### 2.1 图片处理失败
**位置**: `ApprovalOperateService.approval()`
```java
// 图片下载失败
if (preOrder.getPreOrderState() == PreOrderState.AUDIT_REJECT) {
    result.setApprovalStatus(ApprovalStatus.AUDIT_REJECT.name());
    result.setRejectReason(preOrder.getRemark());
    return result;
}
```

#### 2.2 在途订单检查失败
**位置**: `CheckService.onOrderOrRiskByCertNo()`
```java
String onOrder = checkService.onOrderOrRiskByCertNo(preOrder);
if (StringUtil.isNotBlank(onOrder)) {
    //进件失败
    preOrder.setPreOrderState(PreOrderState.AUDIT_REJECT);
    preOrder.setIsReject(WhetherState.Y);
    preOrder.setRemark(onOrder);
    preOrderRepository.save(preOrder);
    return result;
}
```

#### 2.3 用户注册异常
**位置**: `ApprovalOperateService.approval()`
```java
UserRegister userRegister = userService.findUserRegisterByOpenId(preOrder.getOpenId(), preOrder.getFlowChannel());
if (Objects.isNull(userRegister)) {
    userRegister = userService.registerRecord(preOrder.getMobile(), preOrder.getOpenId(), preOrder.getFlowChannel());
}
```

#### 2.4 风控记录创建异常
**位置**: `ApprovalOperateService.approval()`
```java
UserRiskRecord riskRecord = riskService.createRiskRecord(userRegister.getUserId(), preOrder);
```

#### 2.5 MQ消息发送失败
**位置**: `ApprovalOperateService.approval()`
```java
// 签章 和 内部风控
mqService.submitRiskApply(riskRecord.getId());

//更新预订单为审核中
preOrder.setPreOrderState(PreOrderState.AUDITING);
preOrderRepository.save(preOrder);
```

**关键问题**: 如果`mqService.submitRiskApply()`调用成功但后续的状态更新失败，或者在更新状态之前发生异常，预订单就会停留在INIT状态。

#### 2.6 签章处理异常
**位置**: `RiskService.isRiskAgreementAllSuccess()`
```java
public boolean isRiskAgreementAllSuccess(UserRiskRecord userRiskRecord) {
    String userRiskRecordId = userRiskRecord.getId();
    List<AgreementSignRelation> agreementSignRelations = agreementSignRelationRepository.findByRelatedId(userRiskRecordId);
    List<String> signApplyIdList = agreementSignRelations.stream().map(AgreementSignRelation::getSignApplyId)
        .collect(Collectors.toList());

    //签章成功后会复制到user_file表
    List<UserFile> userFiles = userFileRepository.findAllById(signApplyIdList);
    return true; // 注意：这里直接返回true，可能存在问题
}
```

## 🔧 排查步骤

### 1. 完整排查SQL脚本
```sql
-- 1. 检查预订单基本信息
SELECT
    id, order_no, pre_order_state, risk_id, open_id, cert_no, name, mobile,
    apply_time, created_time, updated_time, remark
FROM pre_order
WHERE order_no = 'POR25080220032026823700734336379';

-- 2. 检查风控记录状态
SELECT
    urr.id, urr.user_id, urr.approve_result, urr.created_time, urr.updated_time,
    urr.remark, urr.risk_final_result, urr.pass_time
FROM user_risk_record urr
WHERE urr.id = (
    SELECT risk_id FROM pre_order WHERE order_no = 'POR25080220032026823700734336379'
);

-- 3. 检查用户注册信息
SELECT
    ur.id, ur.user_id, ur.open_id, ur.mobile, ur.flow_channel,
    ur.created_time, ur.register_state
FROM user_register ur
WHERE ur.open_id = (
    SELECT open_id FROM pre_order WHERE order_no = 'POR25080220032026823700734336379'
);

-- 4. 检查用户基本信息
SELECT
    ui.id, ui.name, ui.cert_no, ui.mobile, ui.created_time
FROM user_info ui
WHERE ui.id = (
    SELECT ur.user_id FROM user_register ur
    WHERE ur.open_id = (
        SELECT open_id FROM pre_order WHERE order_no = 'POR25080220032026823700734336379'
    )
);

-- 5. 检查签章记录
SELECT
    asr.id, asr.risk_id, asr.sign_state, asr.agreement_type, asr.file_type,
    asr.created_time, asr.updated_time, asr.fail_reason, asr.common_task_id
FROM agreement_signature_record asr
WHERE asr.risk_id = (
    SELECT risk_id FROM pre_order WHERE order_no = 'POR25080220032026823700734336379'
);

-- 6. 检查签章关联关系
SELECT
    asr2.id, asr2.related_id, asr2.sign_apply_id, asr2.loan_stage
FROM agreement_sign_relation asr2
WHERE asr2.related_id = (
    SELECT risk_id FROM pre_order WHERE order_no = 'POR25080220032026823700734336379'
);

-- 7. 检查用户文件记录
SELECT
    uf.id, uf.user_id, uf.file_type, uf.oss_bucket, uf.oss_key,
    uf.created_time, uf.file_status
FROM user_file uf
WHERE uf.user_id = (
    SELECT ur.user_id FROM user_register ur
    WHERE ur.open_id = (
        SELECT open_id FROM pre_order WHERE order_no = 'POR25080220032026823700734336379'
    )
)
ORDER BY uf.created_time DESC;

-- 8. 检查是否存在相关订单
SELECT
    o.id, o.order_no, o.order_state, o.risk_id, o.user_id,
    o.created_time, o.updated_time, o.remark
FROM `order` o
WHERE o.risk_id = (
    SELECT risk_id FROM pre_order WHERE order_no = 'POR25080220032026823700734336379'
);
```

### 2. 日志排查关键词
在应用日志中搜索以下关键词：
- `POR25080220032026823700734336379` (订单号)
- `申请风控, riskId: [具体的风控ID]`
- `风控记录, riskId: [风控ID], 用户id 缺失`
- `风控挂起`
- `签章处理异常`
- `用户注册异常`
- `图片上传失败`

## 🚨 常见异常场景

### 场景1: 风控挂起
```java
//风控挂起
if (riskSuspend) {
    record.setApproveResult(AuditState.SUSPEND);
    riskRecordRepository.save(record);
    return;
}
```

### 场景2: 风控记录缺失用户ID
```java
if (record == null || record.getUserId() == null) {
    getMqWarningService().warn("风控记录, riskId: " + riskId + ", 用户id 缺失");
    return;
}
```

### 场景3: 签章处理延迟
```java
public void platformRisk(final UserRiskRecord record) throws HttpException, JsonProcessingException {
    boolean allSuccess = isRiskAgreementAllSuccess(record);
    logger.info("用户风控表 id: [{}],  所有签章结果: [{}]", record.getId(), allSuccess);
    if (allSuccess) {
        // 开始走风控系统
        platformRiskInterAsync(record);
    } else {
        mqService.submitRiskApplyDelay(record.getId()); // 延迟处理
    }
}
```

## 💡 解决方案

### 1. 立即处理方案
对于当前停留的订单，可以手动触发风控处理：
```java
// 查找风控记录ID
String riskId = "从数据库查询得到的风控ID";
// 手动提交风控申请
mqService.submitRiskApply(riskId);
```

### 2. 预防措施
1. **增强异常处理**: 在关键步骤添加try-catch和事务回滚
2. **添加监控告警**: 监控INIT状态超过一定时间的预订单
3. **完善日志记录**: 记录每个处理步骤的详细信息
4. **定时任务修复**: 创建定时任务处理异常状态的订单

### 3. 代码优化建议
```java
// 在ApprovalOperateService.approval()中添加更完善的异常处理
try {
    mqService.submitRiskApply(riskRecord.getId());

    //更新预订单为审核中
    preOrder.setPreOrderState(PreOrderState.AUDITING);
    preOrderRepository.save(preOrder);

    logger.info("预订单状态更新成功，订单号: {}, 风控ID: {}", preOrder.getOrderNo(), riskRecord.getId());
} catch (Exception e) {
    logger.error("风控申请或状态更新失败，订单号: {}", preOrder.getOrderNo(), e);
    // 回滚或重试逻辑
    throw new BizException(ResultCode.SYSTEM_ERROR);
}
```

## 📊 监控指标

建议添加以下监控指标：
1. INIT状态超过30分钟的预订单数量
2. 风控申请MQ消息处理失败率
3. 签章处理超时数量
4. 用户注册失败率

## 🔄 后续优化

1. **状态机优化**: 考虑使用状态机模式管理预订单状态流转
2. **幂等性保证**: 确保重复处理不会产生副作用
3. **补偿机制**: 添加自动修复异常状态的补偿逻辑
4. **分布式锁**: 防止并发处理导致的状态不一致
